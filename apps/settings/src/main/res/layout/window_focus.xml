<?xml version="1.0" encoding="utf-8"?><!--对焦画面-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1920px"
    android:layout_height="1080px"
    tools:ignore="PxUsage">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitStart"
        android:src="@drawable/img_focus_bg" />

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/manualFocusTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50px"
        android:text="@string/manual_focus"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/focusHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30px"
        android:text="@string/focus_hint_auto"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/adjustModeFineTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_goneMarginBottom="128px" />

    <TextView
        android:id="@+id/focusingHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/focusHintTv" />

    <TextView
        android:id="@+id/adjustModeFineTv"
        android:layout_width="200px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:gravity="center"
        android:text="@string/focus_adjust_fine"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/adjustModeNormalTv" />

    <TextView
        android:id="@+id/adjustModeNormalTv"
        android:layout_width="200px"
        android:layout_height="80px"
        android:gravity="center"
        android:text="@string/focus_adjust_normal"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/adjustModeFineTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/adjustModeFineTv" />


    <ImageView
        android:id="@+id/focusZoomOutIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginLeft="100px"
        android:src="@drawable/ic_focus_zoom_out"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/focusZoomInIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginRight="100px"
        android:src="@drawable/ic_focus_zoom_in"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/manualFocusGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="adjustModeFineTv,focusZoomInIv,focusZoomOutIv,manualFocusTitleTv,titleBar" />

</androidx.constraintlayout.widget.ConstraintLayout>