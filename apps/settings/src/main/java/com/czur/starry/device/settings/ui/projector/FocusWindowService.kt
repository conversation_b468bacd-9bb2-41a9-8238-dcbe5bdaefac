package com.czur.starry.device.settings.ui.projector

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.text.Spannable
import android.text.SpannableString
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.core.content.ContextCompat
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.CenteredImageSpan
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.manager.FocusManager
import com.czur.starry.device.settings.utils.getStr
import com.czur.uilib.CZTitleBar
import com.czur.uilib.choose.CZImageCheckBox
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2023/3/24
 */
class FocusWindowService : AlertWindowService() {
    override val windowType: Int = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
    override val layoutId: Int = R.layout.window_focus
    override val windowWidthParam: Int = getScreenWidth()
    override val windowHeightParam: Int = getScreenHeight()
    override val careKeyEvent: Boolean = true

    companion object {
        private const val TAG = "FocusActivity"
        private const val MODE_NONE = -1
        const val MODE_AUTO = 0
        const val MODE_MANUAL = 1
        private const val KEY_MODE = "mode"

        // 自动对焦完成后, 默认延迟关闭的时间
        // 让UI显得不是那么突兀
        private const val FINISH_DELAY = 1 * ONE_SECOND

        private const val ACTION_CZUR_APP_RESUME = "czur.intent.action.APP_RESUME"
        private const val CATEGORY_LAUNCHER = "czur.intent.category.LAUNCHER"
        private const val CATEGORY_RECENT = "czur.intent.category.RECENT"

        fun start(mode: Int) {
            // 模式不对, 不启动Activity
            if (mode != MODE_AUTO && mode != MODE_MANUAL) return

            val context = CZURAtyManager.appContext
            val intent = Intent().apply {
                putExtra(KEY_MODE, mode)
                setClass(context, FocusWindowService::class.java)
            }
            context.startService(intent)
        }
    }

    /**
     * 调整模式
     */
    private enum class AdjustMode() {
        FINE,   // 微调
        NORMAL; // 正常

        val step: Int
            get() {
                return when (Constants.starryHWInfo.series) {
                    Q1Series -> when (this) {
                        FINE -> 1
                        NORMAL -> 10
                    }

                    Q2Series -> when (this) {
                        FINE -> 5
                        NORMAL -> 30
                    }

                    else -> when (this) {
                        FINE -> 1
                        NORMAL -> 10
                    }
                }
            }
    }

    private var isFocusing = false

    // UI显示模式
    private var mode: Int = MODE_NONE
    private val focusManager: FocusManager<FocusWindowService> by lazy {
        FocusManager(this)
    }

    // 手动调整模式
    private var adjustMode = AdjustMode.NORMAL
        set(value) {
            if (field == value) return
            field = value
        }

    private val adjustModeFineTv by ViewFinder<TextView>(R.id.adjustModeFineTv)
    private val adjustModeNormalTv by ViewFinder<TextView>(R.id.adjustModeNormalTv)
    private val adjustModeFineBox by ViewFinder<CZImageCheckBox>(R.id.adjustModeFineBox)
    private val adjustModeNormalBox by ViewFinder<CZImageCheckBox>(R.id.adjustModeNormalBox)
    private val manualFocusGroup by ViewFinder<Group>(R.id.manualFocusGroup)
    private val focusingHintTv by ViewFinder<TextView>(R.id.focusingHintTv)
    private val focusHintTv by ViewFinder<TextView>(R.id.focusHintTv)
    private val focusZoomInIv by ViewFinder<ImageView>(R.id.focusZoomInIv)
    private val focusZoomOutIv by ViewFinder<ImageView>(R.id.focusZoomOutIv)
    private val titleBar by ViewFinder<CZTitleBar>(R.id.titleBar)
    private val manualCorrectionVi by ViewFinder<View>(R.id.manualCorrectionVi)

    private val resumeReceiver by lazy {
        ResumeReceiver()
    }

    override fun needBlockDisplay(): Boolean {
        // Studio 不需要显示
        return Constants.starryHWInfo.series == StudioSeries
    }


    override fun View.initViews() {

        adjustModeFineTv.setOnClickListener {
            updateAdjustMode(AdjustMode.FINE)
        }

        adjustModeFineBox.setOnClickListener {
            updateAdjustMode(AdjustMode.FINE)
        }

        adjustModeNormalTv.setOnClickListener {
            updateAdjustMode(AdjustMode.NORMAL)
        }

        adjustModeNormalBox.setOnClickListener {
            updateAdjustMode(AdjustMode.NORMAL)
        }

        focusZoomInIv.setOnClickListener {
            // 加
            focusManager.moveForward(adjustMode.step)
        }

        focusZoomOutIv.setOnClickListener {
            // 减
            focusManager.moveBackward(adjustMode.step)
        }

        titleBar.onBackClick = {
            stopSelf()
        }

        manualCorrectionVi.setOnDebounceClickListener {
            val context = this@FocusWindowService
            val intent = Intent(context, KeystoneManualActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context.startActivity(intent)
            launch {
                // 延迟一段时间, 让Activity有时间启动
                delay(500)
                // 关闭对焦窗口
                logTagD(TAG, "手动矫正后, 关闭对焦窗口")
                stopSelf()
            }
        }
    }

    private fun updateAdjustMode(mode: AdjustMode) {
        adjustMode = mode
        adjustModeFineBox.isChecked = (mode == AdjustMode.FINE)
        adjustModeNormalBox.isChecked = (mode == AdjustMode.NORMAL)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 检查启动参数
        // 没指定参数就是自动对焦
        if (needBlockDisplay()) {
            logTagD(TAG, "Studio不需要显示对焦窗口")
            stopSelf()
            return super.onStartCommand(intent, flags, startId)
        }
        when (val preMode = intent?.getIntExtra(KEY_MODE, MODE_AUTO)) {
            MODE_AUTO, MODE_MANUAL -> {
                if (mode != MODE_NONE) {
                    logTagV(TAG, "已经启动过了, 不再重复启动")
                    return super.onStartCommand(intent, flags, startId)
                }
                mode = preMode
                initViewByMode()
                initDataByMode()
            }

            else -> {
                logTagW(TAG, "启动参数有错误:$mode")
                stopSelf()
            }
        }
        return super.onStartCommand(intent, flags, startId)
    }


    private fun initViewByMode() {
        when (mode) {
            MODE_AUTO -> {
                manualFocusGroup.visibility = View.GONE
                if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                    focusingHintTv.gone()
                } else {
                    val expand = 3
                    val imgDrawable =
                        ContextCompat.getDrawable(this, R.drawable.ic_focus_white)?.apply {
                            setBounds(0, 0, minimumWidth + expand, minimumHeight + expand)
                        }
                    val hintText = getString(R.string.auto_focusing_hint)
                    setForceImage(imgDrawable!!, focusingHintTv, hintText)

                    focusingHintTv.show()
                }
                focusHintTv.text = getStr(R.string.focus_hint_auto)

            }

            MODE_MANUAL -> {
                manualFocusGroup.visibility = View.VISIBLE
                focusingHintTv.gone()
                focusHintTv.text = getStr(
                    if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD)
                        R.string.force_hint_manual_army_build
                    else
                        R.string.force_hint_manual
                )
            }
        }
    }

    private fun initDataByMode() {
        if (isFocusing) {
            logTagD(TAG, "正在对焦中...忽略")
            return
        }
        isFocusing = true

        when (mode) {
            MODE_AUTO -> focusAuto()
            MODE_MANUAL -> {
                registerResumeReceiver()
                NoticeHandler.register(MsgType(MsgType.MEET, MsgType.MEET_CALL_VIDEO), this) {
                    logTagD(TAG, "收到视频通话消息, 关闭对焦窗口")
                    stopSelf()
                }
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        // 自动对焦过程中, 屏蔽任何按键, 啥都操作不了
        return if (mode == MODE_AUTO) {
            true
        } else {
            when (keyCode) {
                KeyEvent.KEYCODE_VOLUME_UP -> {
                    logTagD(TAG, "对焦+")
                    focusManager.moveForward(adjustMode.step)
                    true
                }

                KeyEvent.KEYCODE_VOLUME_DOWN -> {
                    logTagD(TAG, "对焦-")
                    focusManager.moveBackward(adjustMode.step)
                    true
                }

                KeyEvent.KEYCODE_BACK -> stopSelf().let { true }

                else -> super.onKeyDown(keyCode, event)
            }
        }
    }

    private fun setForceImage(imgDrawable: Drawable, tv: TextView, text: String) {
        val placeholderCharacter = "[focus]"
        val index = text.indexOf("[focus]")
        val span = SpannableString(text)

        val imgSpan = CenteredImageSpan(imgDrawable)
        span.setSpan(
            imgSpan,
            index,
            index + placeholderCharacter.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )

        tv.text = span
    }

    /**
     * 获取圆角
     */
    private fun getShapeDrawable(
        color: Int,
        tl: Float,
        tr: Float,
        br: Float,
        bl: Float,
    ): GradientDrawable {
        val gradientDrawable = GradientDrawable()
        gradientDrawable.cornerRadii = floatArrayOf(tl, tl, tr, tr, br, br, bl, bl)
        gradientDrawable.setColor(color)
        return gradientDrawable

    }

    /**
     * 开始自动对焦
     */
    private fun focusAuto(needRetry: Boolean = true) {
        logTagD(TAG, "开始自动对焦")
        focusManager.startAutoFocus {
            if (it) {
                logTagV(TAG, "对焦成功")
                toast(R.string.toast_focus_success)
                finishDelay()
            } else {
                logTagD(TAG, "对焦失败")
                if (needRetry) {
                    resetFocusAndeFocusAuto()
                } else {
                    toast(R.string.toast_focus_fail)
                    finishDelay()
                }
            }
        }
    }

    private fun resetFocusAndeFocusAuto() {
        logTagD(TAG, "重置光机, 并重新对焦")
        focusManager.resetAutoFocus {
            if (it) {
                logTagV(TAG, "重置对焦成功")
                focusAuto(false)
            } else {
                logTagD(TAG, "重置对焦失败")
                toast(R.string.toast_focus_fail)
                finishDelay()
            }
        }
    }

    override fun onDestroy() {
        if (mode == MODE_MANUAL) {
            logTagD(TAG, "取消注册广播")
            unregisterReceiver(resumeReceiver)
        }
        super.onDestroy()
    }

    private fun registerResumeReceiver() {
        logTagD(TAG, "注册广播")
        val filter = IntentFilter()
        filter.addAction(ACTION_CZUR_APP_RESUME)
        filter.addCategory(CATEGORY_LAUNCHER)
        filter.addCategory(CATEGORY_RECENT)
        registerReceiver(resumeReceiver, filter)
    }

    /**
     * 延迟默认时间后 finish
     */
    private fun finishDelay() {
        logTagD(TAG, "finishDelay")
        launch {
            delay(FINISH_DELAY)
            stopSelf()
        }
    }

    private inner class ResumeReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            logTagD(TAG, "onReceive: $intent")
            if (ACTION_CZUR_APP_RESUME == intent.action) {
                stopSelf()
            }
        }
    }

}