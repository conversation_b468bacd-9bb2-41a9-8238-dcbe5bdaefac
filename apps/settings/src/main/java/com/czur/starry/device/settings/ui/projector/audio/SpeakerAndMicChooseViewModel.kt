package com.czur.starry.device.settings.ui.projector.audio

import android.app.Application
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.utils.displayName
import com.czur.starry.device.settings.utils.isBluetoothDevice
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

/**
 * Created by 陈丰尧 on 2025/4/7
 */
private const val TAG = "SpeakerAndMicChooseViewModel"

class SpeakerAndMicChooseViewModel(application: Application) : AndroidViewModel(application) {
    private val audioManager by lazy {
        application.getSystemService(Application.AUDIO_SERVICE) as AudioManager
    }

    private val _audioDeviceListFlow = MutableStateFlow<List<AudioDeviceInfo>>(emptyList())
    private val _currentSelSpeakerTypeFlow = MutableStateFlow(-1)
    private val _currentSelMicTypeFlow = MutableStateFlow(-1)

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    /**
     * 检查设备类型是否匹配，处理蓝牙设备的特殊情况
     * 蓝牙设备的 SCO 和 A2DP 类型被认为是同一个设备
     */
    private fun isDeviceTypeMatched(deviceType: Int, targetType: Int): Boolean {
        if (deviceType == targetType) {
            return true
        }

        // 处理蓝牙设备的特殊情况：SCO 和 A2DP 互相匹配
        val bluetoothTypes = setOf(AudioDeviceInfo.TYPE_BLUETOOTH_SCO, AudioDeviceInfo.TYPE_BLUETOOTH_A2DP)
        return deviceType in bluetoothTypes && targetType in bluetoothTypes
    }

    /**
     * 根据设备类型查找对应的音频设备
     * @param deviceType 目标设备类型
     * @param isSink true为扬声器，false为麦克风
     * @return 匹配的设备，如果没有找到则返回null
     */
    private fun findDeviceByType(deviceType: Int, isSink: Boolean): AudioDeviceInfo? {
        if (deviceType == -1) {
            return null
        }

        return _audioDeviceListFlow.value.firstOrNull { device ->
            device.isSink == isSink && isDeviceTypeMatched(device.type, deviceType)
        }
    }

    val speakerDeviceFlow = _audioDeviceListFlow.map { list ->
        list.filter {
            it.isSink && it.type != AudioDeviceInfo.TYPE_BLUETOOTH_SCO
        }
    }.stateIn(
        viewModelScope,
        initialValue = emptyList(),
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    val micDeviceFlow = _audioDeviceListFlow.map { list ->
        list.filter {
            !it.isSink
        }
    }.stateIn(
        viewModelScope,
        initialValue = emptyList(),
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    val selSpeakerIndexFlow = combine(_currentSelSpeakerTypeFlow, speakerDeviceFlow) { deviceType, speakers ->
        if (speakers.isEmpty()) {
            return@combine -1
        }
        var index = speakers.indexOfFirst { device ->
            isDeviceTypeMatched(device.type, deviceType)
        }
        if (index == -1) {
            logTagW(TAG, "没有找到对应的Speaker类型:$deviceType, 可用设备: ${speakers.map { it.displayName + "(${it.type})" }}")
            index = speakers.indexOfFirst {
                it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
            }
        }
        index.coerceAtLeast(0)
    }.stateIn(
        viewModelScope,
        initialValue = 0,
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    val selMicIndexFlow = combine(_currentSelMicTypeFlow, micDeviceFlow) { deviceType, mics ->
        var index = mics.indexOfFirst { device ->
            isDeviceTypeMatched(device.type, deviceType)
        }
        if (index == -1) {
            logTagW(TAG, "没有找到对应的Mic类型:$deviceType, 可用设备: ${mics.map { it.displayName + "(${it.type})" }}")
            index = mics.indexOfFirst {
                it.type == AudioDeviceInfo.TYPE_BUILTIN_MIC
            }
        }
        index.coerceAtLeast(0)
    }.stateIn(
        viewModelScope,
        initialValue = 0,
        started = kotlinx.coroutines.flow.SharingStarted.Lazily
    )

    private val audioDeviceCb = object : AudioDeviceCallback() {
        override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesAdded(addedDevices)
            updateDeviceList()
        }

        override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesRemoved(removedDevices)
            updateDeviceList()
        }
    }

    init {
        launch {
            audioManager.registerAudioDeviceCallback(audioDeviceCb, null)
        }

        launch {
            _currentSelSpeakerTypeFlow.value = SettingUtil.CameraAndMicSetting.getDefaultSpeakerType()
            _currentSelMicTypeFlow.value = SettingUtil.CameraAndMicSetting.getDefaultMicType()
        }
    }

    /**
     * 获取属于同一设备对应音频设备, 比如出入一个扬声器,找到对应的Mic
     * @param targetDevice 目标设备
     * @return 对应的设备信息,如果不存在则为Null
     */
    fun getPairedDeviceInfo(targetDevice: AudioDeviceInfo): AudioDeviceInfo? {
        val targetDevice = _audioDeviceListFlow.value
            .filterNot {
                it.isSink && it.type == AudioDeviceInfo.TYPE_BLUETOOTH_SCO
            }
            .firstOrNull { it.address == targetDevice.address && it.isSink != targetDevice.isSink }
        return targetDevice
    }

    suspend fun updateToBuiltInDevice() {
        logTagI(TAG, "updateToBuiltInDevice")
        val builtInSpeaker =
            _audioDeviceListFlow.value.firstOrNull { it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER }
        val builtInMic =
            _audioDeviceListFlow.value.firstOrNull { it.type == AudioDeviceInfo.TYPE_BUILTIN_MIC }
        if (builtInSpeaker != null) {
            updateSelDevice(builtInSpeaker, builtInMic)
        } else if (builtInMic != null) {
            updateSelDevice(builtInMic, builtInSpeaker)
        } else {
            logTagW(TAG, "没有找到内置设备")
        }
    }

    suspend fun updateSelDevice(
        device: AudioDeviceInfo,
        withDevice: AudioDeviceInfo? = null,
        needCheck: Boolean = true
    ) {
        logTagD(
            TAG,
            "updateSelDevice:${device.displayName} ${device.isSink},withDevice:${withDevice?.displayName}"
        )
        var realWithDevice = withDevice

        if (withDevice == null && needCheck) {
            // 用户没有指定同步修改, 需要检查当前是否是蓝牙设备
            val currentDevice = if (device.isSink)
                speakerDeviceFlow.value.getOrNull(selSpeakerIndexFlow.value)
            else
                micDeviceFlow.value.getOrNull(selMicIndexFlow.value)
            if (currentDevice?.isBluetoothDevice == true) {
                logTagD(TAG, "当前是蓝牙设备,需要同步修改")
                realWithDevice = if (device.isSink) {
                    logTagD(TAG, "需要回到系统mic")
                    _audioDeviceListFlow.value.firstOrNull {
                        it.type == AudioDeviceInfo.TYPE_BUILTIN_MIC
                    }
                } else {
                    logTagD(TAG, "需要回到系统扬声器")
                    _audioDeviceListFlow.value.firstOrNull {
                        it.type == AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
                    }
                }
                logTagD(TAG, "同步自动修改设备:${realWithDevice?.displayName}")
            }
        }


        val res = systemManager.setPreferredAudioDevice(device)
        if (res) {
            logTagD(TAG, "设置成功:${device.displayName}, 类型:${device.type}")
            // 系统会自动写入 persist.czur.outtype 和 persist.czur.intype
            // APP只需要更新本地的状态用于UI显示
            if (device.isSink) {
                _currentSelSpeakerTypeFlow.value = device.type
            } else {
                _currentSelMicTypeFlow.value = device.type
            }

            realWithDevice?.let {
                logTagD(TAG, "设置对应的另一个设备")
                updateSelDevice(it, null, needCheck = false)
            }

        } else {
            logTagW(TAG, "设置失败")
        }
    }

    /**
     * 更新音频设备列表
     */
    fun updateDeviceList() {
        val audioDevices =
            audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS or AudioManager.GET_DEVICES_INPUTS)
        _audioDeviceListFlow.value = audioDevices.filter {
            if (it.type == AudioDeviceInfo.TYPE_REMOTE_SUBMIX) {
                return@filter false
            }
            if (!it.isSink && (it.type == AudioDeviceInfo.TYPE_HDMI ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_ARC ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_ARC ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_EARC)
            ) {
                return@filter false
            }
            true
        }

        // 设备列表更新后，重新读取系统属性以获取最新的选中设备
        launch {
            _currentSelSpeakerTypeFlow.value = SettingUtil.CameraAndMicSetting.getDefaultSpeakerType()
            _currentSelMicTypeFlow.value = SettingUtil.CameraAndMicSetting.getDefaultMicType()
        }
    }

    override fun onCleared() {
        audioManager.unregisterAudioDeviceCallback(audioDeviceCb)
        super.onCleared()
    }
}