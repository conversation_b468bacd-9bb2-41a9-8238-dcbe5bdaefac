package com.czur.starry.device.settings.ui.projector.brightness

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentBrightnessBinding

/**
 * Created by 陈丰尧 on 2025/5/28
 */
class ScreenBrightnessFragment : BaseBindingMenuFragment<FragmentBrightnessBinding>() {
    private val brightnessViewModel: ScreenBrightnessViewModel by viewModels()

    override fun FragmentBrightnessBinding.initBindingViews() {

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
    }
}