package com.czur.starry.device.settings.ui.projector.brightness

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentBrightnessBinding
import com.czur.starry.device.settings.ui.projector.brightness.ScreenBrightnessViewModel.Companion.BRIGHTNESS_MAX
import com.czur.starry.device.settings.ui.projector.brightness.ScreenBrightnessViewModel.Companion.BRIGHTNESS_MIN

/**
 * Created by 陈丰尧 on 2025/5/28
 */
private const val TAG = "ScreenBrightnessFragment"

class ScreenBrightnessFragment : BaseBindingMenuFragment<FragmentBrightnessBinding>() {
    private val brightnessViewModel: ScreenBrightnessViewModel by viewModels()

    override fun FragmentBrightnessBinding.initBindingViews() {
        // 设置 SeekBar 的范围
        brightnessSeekBar.apply {
            min = BRIGHTNESS_MIN
            max = BRIGHTNESS_MAX

            // 进度改变的回调（拖动过程中）
            onProgressChangerListener = { progress, fromUser ->
                logTagD(TAG, "亮度进度改变: $progress, fromUser: $fromUser")
                // 在拖动过程中不做处理，等待拖动完成
            }

            // 进度改变完成的回调（拖动完成或点击按钮）
            onProgressChangeCompletedListener = { progress, fromUser ->
                logTagD(TAG, "亮度进度改变完成: $progress, fromUser: $fromUser")
                if (fromUser) {
                    // 只有用户操作才设置亮度
                    brightnessViewModel.setBrightness(progress)
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        // 监听亮度变化
        repeatCollectOnResume(brightnessViewModel.brightnessFlow) { brightness ->
            logTagD(TAG, "亮度更新: $brightness")
            // 更新 SeekBar 的进度，但不触发用户回调
            binding.brightnessSeekBar.progress = brightness
        }

        // 监听加载状态
        repeatCollectOnResume(brightnessViewModel.isLoadingFlow) { isLoading ->
            logTagD(TAG, "加载状态: $isLoading")
            // 加载时禁用用户操作
            binding.brightnessSeekBar.blockUserOperation = isLoading
        }
    }
}