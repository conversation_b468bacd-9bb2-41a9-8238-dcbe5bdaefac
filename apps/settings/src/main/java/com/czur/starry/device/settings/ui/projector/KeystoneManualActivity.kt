package com.czur.starry.device.settings.ui.projector

import android.view.View
import androidx.constraintlayout.widget.Group
import androidx.core.view.isVisible
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.databinding.ActivityKeystoneManualBinding
import com.czur.starry.device.settings.manager.DisplayManager
import com.czur.starry.device.settings.manager.DisplayManager.KEYSTONE_DEFAULT_CLICK_STEP
import com.czur.starry.device.settings.utils.fibonacci
import com.czur.starry.device.settings.widget.HoldImageView
import com.czur.starry.device.settings.widget.HoldImageView.OnHoldListener
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * Created by 陈丰尧 on 2/2/21
 * 手动梯形矫正页面
 */
class KeystoneManualActivity : CZViewBindingAty<ActivityKeystoneManualBinding>() {
    companion object {
        private const val TAG = "KeystoneManualActivity"
        private const val HOLD_TIME_INTERVAL = 350L
    }

    private val adjustViews by lazy { binding.initAdjustViews() }
    private val adjust4DsViews by lazy { binding.initAdjust4DsViews() }
    var adjust4DsJob: Job? = null

    private var needCloseAutoKeystone = true


    override fun ActivityKeystoneManualBinding.initBindingViews() {
        backBtn.setOnClickListener {
            finish()
        }
        checkVisibilityAndStatus()
        initClick()
    }

    private fun initClick() {

        adjust4DsViews.forEach { adjust4DsView ->
            val view = adjust4DsView.clickView
            val direction = adjust4DsView.direction
            view.setOnClickListener {
                logTagD(TAG, "中间方向键的点击:$direction")
                launch {
                    DisplayManager.updateKeystoneTranslation(
                        direction,
                        closeAutoKeystone = needCloseAutoKeystone,
                    )
                    checkVisibilityAndStatus()
                }
            }

            view.onHoldListener = object : OnHoldListener {
                var stepIterator: Iterator<Long>? = null
                override fun onHoldStart() {
                    logTagD(TAG, "adjust4DsViews onHoldStart")
                    // 使用斐波那契数列,让移动速度逐渐增大
                    stepIterator = fibonacci().iterator()
                    adjust4DsJob = launch {
                        while (isActive) {
                            val step = stepIterator?.next() ?: KEYSTONE_DEFAULT_CLICK_STEP
                            logTagD(TAG, "adjust4DsViews step:${step}")
                            DisplayManager.updateKeystoneTranslation(
                                direction,
                                step,
                                closeAutoKeystone = needCloseAutoKeystone,
                            )
                            checkVisibilityAndStatus()
                            delay(HOLD_TIME_INTERVAL)
                        }
                        logTagD(TAG, "adjust4DsViews 退出循环")
                    }
                }

                override fun onHoldStop() {
                    logTagD(TAG, "adjust4DsViews onHoldStop")
                    adjust4DsJob?.cancel()
                    adjust4DsJob = null
                    checkVisibilityAndStatus { it.direction == direction }
                }
            }
        }

        adjustViews.forEach { adjustView ->
            val view = adjustView.clickView
            val location = adjustView.location
            val direction = adjustView.direction
            view.setOnClickListener {
                logTagD(TAG, "点击:$location - $direction")
                DisplayManager.updateKeystone(
                    location,
                    direction,
                    closeAutoKeystone = needCloseAutoKeystone
                )
                checkVisibilityAndStatus(getVisibilityFilter(location))
            }
            view.onHoldListener = object : OnHoldListener {
                var stepIterator: Iterator<Long>? = null
                var adjustJob: Job? = null

                override fun onHoldStart() {
                    logTagD(TAG, "onHoldStart")
                    // 使用斐波那契数列,让移动速度逐渐增大
                    stepIterator = fibonacci().iterator()
                    adjustJob = launch {
                        while (isActive) {
                            val step = stepIterator?.next() ?: KEYSTONE_DEFAULT_CLICK_STEP
                            logTagD(TAG, "step:${step}")
                            DisplayManager.updateKeystone(
                                location,
                                direction,
                                step,
                                closeAutoKeystone = needCloseAutoKeystone,
                            )
                            // 长按的时候不判断View的显示
                            // 防止用户正在按的按钮没了
                            // 每次变化要弄个间隔时间
                            delay(HOLD_TIME_INTERVAL)
                        }
                        logTagD(TAG, "退出循环")
                    }
                }

                override fun onHoldStop() {
                    logTagD(TAG, "onHoldStop")
                    adjustJob?.cancel()
                    checkVisibilityAndStatus(getVisibilityFilter(location))
                }

            }
        }
    }

    private fun getVisibilityFilter(point: DisplayManager.KeyStonePoint): (AdjustView) -> Boolean {
        logTagD(TAG, "设置Filter:$point")
        return when (point) {
            DisplayManager.KeyStonePoint.LEFT_TOP -> { adjustView ->
                adjustView.location == point &&
                        (adjustView.direction == DisplayManager.KeyStoneDirection.LEFT ||
                                adjustView.direction == DisplayManager.KeyStoneDirection.UP)
            }

            DisplayManager.KeyStonePoint.RIGHT_TOP -> { adjustView ->
                adjustView.location == point &&
                        (adjustView.direction == DisplayManager.KeyStoneDirection.RIGHT ||
                                adjustView.direction == DisplayManager.KeyStoneDirection.UP)

            }

            DisplayManager.KeyStonePoint.LEFT_BOTTOM -> { adjustView ->
                adjustView.location == point &&
                        (adjustView.direction == DisplayManager.KeyStoneDirection.LEFT ||
                                adjustView.direction == DisplayManager.KeyStoneDirection.DOWN)

            }

            DisplayManager.KeyStonePoint.RIGHT_BOTTOM -> { adjustView ->
                adjustView.location == point &&
                        (adjustView.direction == DisplayManager.KeyStoneDirection.RIGHT ||
                                adjustView.direction == DisplayManager.KeyStoneDirection.DOWN)

            }
        }
    }

    private fun checkVisibilityAndStatus(predicate: (AdjustView) -> Boolean = { _ -> true }) {
        logTagD(TAG, "检查可见性")
        // 检查四个角的可见性(向外)
        adjustViews.filter(predicate).forEach {
            val view = it.showView
            val location = it.location
            val direction = it.direction
            val visibility = DisplayManager.canAdjust(location, direction)
            logTagD(
                TAG,
                "检查可见性:Location:$location ,direction:$direction, visibility: $visibility"
            )
            view.visibility =
                if (visibility) View.VISIBLE else View.INVISIBLE
        }

        val startC2L = binding.groupC2L.isVisible
        val startC2T = binding.groupC2T.isVisible
        val startC2R = binding.groupC2R.isVisible
        val startC2B = binding.groupC2B.isVisible

        // 检查四条边的最小值
        // 上
        val (top, bottom, left, right) = DisplayManager.getKeystoneEdgeLength()
        logTagD(TAG, "top:$top, bottom:$bottom, left:$left, right:$right")

        if (top <= DisplayManager.KEYSTONE_POINT_SPACE_MIN) {
            binding.groupLT2R.visibility = View.INVISIBLE
            binding.groupRT2L.visibility = View.INVISIBLE
        } else {
            binding.groupLT2R.visibility = View.VISIBLE
            binding.groupRT2L.visibility = View.VISIBLE
        }
        // 下
        if (bottom <= DisplayManager.KEYSTONE_POINT_SPACE_MIN) {
            binding.groupLB2R.visibility = View.INVISIBLE
            binding.groupRB2L.visibility = View.INVISIBLE
        } else {
            binding.groupLB2R.visibility = View.VISIBLE
            binding.groupRB2L.visibility = View.VISIBLE
        }
        // 左
        if (left <= DisplayManager.KEYSTONE_POINT_SPACE_MIN) {
            binding.groupLT2B.visibility = View.INVISIBLE
            binding.groupLB2T.visibility = View.INVISIBLE
        } else {
            binding.groupLT2B.visibility = View.VISIBLE
            binding.groupLB2T.visibility = View.VISIBLE
        }
        // 右
        if (right <= DisplayManager.KEYSTONE_POINT_SPACE_MIN) {
            binding.groupRT2B.visibility = View.INVISIBLE
            binding.groupRB2T.visibility = View.INVISIBLE
        } else {
            binding.groupRT2B.visibility = View.VISIBLE
            binding.groupRB2T.visibility = View.VISIBLE
        }

        // 检查中心平移的可见性 - 基于数据判断而不是基于View的可见性
        binding.groupC2L.visibility =
            if (DisplayManager.canTranslate(DisplayManager.KeyStoneDirection.LEFT)) View.VISIBLE else View.INVISIBLE
        binding.groupC2T.visibility =
            if (DisplayManager.canTranslate(DisplayManager.KeyStoneDirection.UP)) View.VISIBLE else View.INVISIBLE
        binding.groupC2R.visibility =
            if (DisplayManager.canTranslate(DisplayManager.KeyStoneDirection.RIGHT)) View.VISIBLE else View.INVISIBLE
        binding.groupC2B.visibility =
            if (DisplayManager.canTranslate(DisplayManager.KeyStoneDirection.DOWN)) View.VISIBLE else View.INVISIBLE

        val endC2L = binding.groupC2L.isVisible
        val endC2T = binding.groupC2T.isVisible
        val endC2R = binding.groupC2R.isVisible
        val endC2B = binding.groupC2B.isVisible

        if (endC2L || endC2T || endC2R || endC2B) {
            binding.pointBgIv.visibility = View.VISIBLE
        } else {
            binding.pointBgIv.visibility = View.INVISIBLE
        }

        if (startC2L != endC2L || startC2T != endC2T || startC2R != endC2R || startC2B != endC2B) {
            adjust4DsJob?.cancel()
            adjust4DsJob = null
        }

        if (needCloseAutoKeystone) {
            launch {
                needCloseAutoKeystone = DisplayManager.getAutoKeystoneStatus()
            }
        }
    }

    private fun ActivityKeystoneManualBinding.initAdjust4DsViews(): List<Adjust4DsView> {
        return listOf(
            Adjust4DsView(
                center2LeftIv,
                DisplayManager.KeyStoneDirection.LEFT,
            ), Adjust4DsView(
                center2TopIv,
                DisplayManager.KeyStoneDirection.UP,
            ), Adjust4DsView(
                center2RightIv,
                DisplayManager.KeyStoneDirection.RIGHT,
            ), Adjust4DsView(
                center2BottomIv,
                DisplayManager.KeyStoneDirection.DOWN,
            )
        )
    }

    private fun ActivityKeystoneManualBinding.initAdjustViews(): List<AdjustView> {
        return listOf(
            // 左上
            AdjustView(
                leftTop2TopIv,
                groupLT2T,
                DisplayManager.KeyStonePoint.LEFT_TOP,
                DisplayManager.KeyStoneDirection.UP
            ),
            AdjustView(
                leftTop2BottomIv,
                groupLT2B,
                DisplayManager.KeyStonePoint.LEFT_TOP,
                DisplayManager.KeyStoneDirection.DOWN
            ),
            AdjustView(
                leftTop2LeftIv,
                groupLT2L,
                DisplayManager.KeyStonePoint.LEFT_TOP,
                DisplayManager.KeyStoneDirection.LEFT
            ),
            AdjustView(
                leftTop2RightIv,
                groupLT2R,
                DisplayManager.KeyStonePoint.LEFT_TOP,
                DisplayManager.KeyStoneDirection.RIGHT
            ),
            // 右上
            AdjustView(
                rightTop2TopIv,
                groupRT2T,
                DisplayManager.KeyStonePoint.RIGHT_TOP,
                DisplayManager.KeyStoneDirection.UP
            ),
            AdjustView(
                rightTop2BottomIv,
                groupRT2B,
                DisplayManager.KeyStonePoint.RIGHT_TOP,
                DisplayManager.KeyStoneDirection.DOWN
            ),
            AdjustView(
                rightTop2LeftIv,
                groupRT2L,
                DisplayManager.KeyStonePoint.RIGHT_TOP,
                DisplayManager.KeyStoneDirection.LEFT
            ),
            AdjustView(
                rightTop2RightIv,
                groupRT2R,
                DisplayManager.KeyStonePoint.RIGHT_TOP,
                DisplayManager.KeyStoneDirection.RIGHT
            ),
            // 左下
            AdjustView(
                leftBottom2TopIv,
                groupLB2T,
                DisplayManager.KeyStonePoint.LEFT_BOTTOM,
                DisplayManager.KeyStoneDirection.UP
            ),
            AdjustView(
                leftBottom2BottomIv,
                groupLB2B,
                DisplayManager.KeyStonePoint.LEFT_BOTTOM,
                DisplayManager.KeyStoneDirection.DOWN
            ),
            AdjustView(
                leftBottom2LeftIv,
                groupLB2L,
                DisplayManager.KeyStonePoint.LEFT_BOTTOM,
                DisplayManager.KeyStoneDirection.LEFT
            ),
            AdjustView(
                leftBottom2RightIv,
                groupLB2R,
                DisplayManager.KeyStonePoint.LEFT_BOTTOM,
                DisplayManager.KeyStoneDirection.RIGHT
            ),
            // 右下
            AdjustView(
                rightBottom2TopIv,
                groupRB2T,
                DisplayManager.KeyStonePoint.RIGHT_BOTTOM,
                DisplayManager.KeyStoneDirection.UP
            ),
            AdjustView(
                rightBottom2BottomIv,
                groupRB2B,
                DisplayManager.KeyStonePoint.RIGHT_BOTTOM,
                DisplayManager.KeyStoneDirection.DOWN
            ),
            AdjustView(
                rightBottom2LeftIv,
                groupRB2L,
                DisplayManager.KeyStonePoint.RIGHT_BOTTOM,
                DisplayManager.KeyStoneDirection.LEFT
            ),
            AdjustView(
                rightBottom2RightIv,
                groupRB2R,
                DisplayManager.KeyStonePoint.RIGHT_BOTTOM,
                DisplayManager.KeyStoneDirection.RIGHT
            ),
        )

    }

    data class AdjustView(
        val clickView: HoldImageView,// 点击事件
        val showView: Group,    // 控制显示的group
        val location: DisplayManager.KeyStonePoint, // 位置
        val direction: DisplayManager.KeyStoneDirection // 方向
    )

    data class Adjust4DsView(
        val clickView: HoldImageView,// 点击事件
        val direction: DisplayManager.KeyStoneDirection // 方向
    )
}