package com.czur.starry.device.settings.ui.personalization

import android.os.Bundle
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentLauncherLayoutBinding
import com.czur.uilib.choose.CZCheckBox

/**
 * Created by 陈丰尧 on 2023/5/18
 */
private const val TAG = "LauncherLayoutFragment"

class LauncherLayoutFragment : BaseBindingMenuFragment<FragmentLauncherLayoutBinding>() {
    private var meetingEnable = false

    override fun FragmentLauncherLayoutBinding.initBindingViews() {
        layoutMeetingCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        layoutTimeCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        layoutStandardCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        
        layoutMeetingCb.setOnCheckedChangeListener { isOn, fromUser ->
            logTagD(TAG, "layoutMeetingCb setOnCheckedChangeListener isOn:$isOn fromUser:$fromUser")
            if (fromUser && isOn) {
                if (meetingEnable) {
                    updateLauncherLayoutMode(SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_MEETING)
                } else {
                    // 视频会议不可用
                    layoutMeetingCb.setChecked(false)
                    toast(R.string.func_coming_soon)
                }
            }
        }
        layoutTimeCb.setOnCheckedChangeListener { isOn, fromUser ->
            logTagD(TAG, "layoutTimeCb setOnCheckedChangeListener isOn:$isOn fromUser:$fromUser")
            if (fromUser && isOn) {
                updateLauncherLayoutMode(SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_TIME)
            }
        }

        layoutStandardCb.setOnCheckedChangeListener { isOn, fromUser ->
            logTagD(TAG, "layoutStandardCb setOnCheckedChangeListener isOn:$isOn fromUser:$fromUser")
            if (fromUser && isOn) {
                updateLauncherLayoutMode(SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_STANDARD)
            }
        }

        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland) {
            launcherLayoutMeetingIv.setImageResource(R.drawable.img_launcher_layout_meeting_mainland)
            launcherLayoutTimeIv.setImageResource(R.drawable.img_launcher_layout_time_mainland)
        }

    }

    private fun updateLauncherLayoutMode(mode: Int) {
        updateUiByLayoutMode(mode)
        launch {
            SettingUtil.PersonalizationSetting.setLauncherLayout(mode)
        }
    }

    private fun updateUiByLayoutMode(mode: Int) {
        when (mode) {
            SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_MEETING -> {
                binding.layoutMeetingCb.setChecked(true)
                binding.layoutTimeCb.setChecked(false)
                binding.layoutStandardCb.setChecked(false)
            }

            SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_TIME -> {
                binding.layoutMeetingCb.setChecked(false)
                binding.layoutTimeCb.setChecked(true)
                binding.layoutStandardCb.setChecked(false)
            }
            SettingUtil.PersonalizationSetting.VALUE_LAUNCHER_LAYOUT_STANDARD -> {
                binding.layoutMeetingCb.setChecked(false)
                binding.layoutTimeCb.setChecked(false)
                binding.layoutStandardCb.setChecked(true)
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        launch {
            val currentLayoutMode = SettingUtil.PersonalizationSetting.getLauncherLayout()
            logTagD(TAG, "currentLauncherLayoutMode = $currentLayoutMode")
            updateUiByLayoutMode(currentLayoutMode)
        }

        launch {
            meetingEnable = false
        }
    }
}