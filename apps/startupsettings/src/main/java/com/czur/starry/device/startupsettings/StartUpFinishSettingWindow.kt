package com.czur.starry.device.startupsettings

import android.app.WallpaperManager
import android.content.ComponentName
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.os.Build
import android.provider.Settings
import android.view.View
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_DEVICE_LAST_VERSION
import com.czur.starry.device.baselib.common.KEY_HDMI_AUTO_OPEN
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.VersionUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import com.czur.starry.device.baselib.utils.update
import com.czur.starry.device.file.filelib.FileHandler
import com.czur.starry.device.sharescreen.esharelib.DEF_E_SHARE_NAME
import com.czur.starry.device.sharescreen.esharelib.PIN_CODE_MODE_DISABLE
import com.eshare.serverlibrary.api.EShareServerSDK
import com.eshare.serverlibrary.api.IEShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlin.system.exitProcess

/**
 * Created by 陈丰尧 on 2024/7/9
 * 使用悬浮窗, 可以在画面切换的时候挡住底层的画面,防止出现黑屏
 */
class StartUpFinishSettingWindow : AlertWindowService() {
    override val layoutId: Int
        get() = R.layout.fragment_startup_finish
    override val windowWidthParam: Int
        get() = getScreenWidth()
    override val windowHeightParam: Int
        get() = getScreenHeight()

    companion object {
        private const val TAG = "StartUpFinishFragment"

        private const val DELAY_FINISH_TIME = 15 * ONE_SECOND

    }

    private val systemManger by lazy { SystemManagerProxy() }

    private val taskList = listOf(
        ::enableLauncher,
        ::saveVersion,
        ::initScreenShare,
        ::initOtherSettings,
        ::copyExplainFile,
        ::setSystemWallpaper,
        ::setCZURAppConfig,
        ::otherSystemSettings,
        ::markSetupComplete,
        ::disableSelf,  // 这个需要在最后?不知道是不是系统的bug
    )

    override fun View.initViews() {}

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_NOT_STICKY
    }

    override fun initData() {
        super.initData()

        launch {
            val taskListDeferred = async { executeSetupTasks() }
            val delayFinishDeferred = async { delayFinish() }
            taskListDeferred.await()
            delayFinishDeferred.await()
            logTagD(TAG, "全部设定任务已完成")
            logTagD(TAG, "结束初期设定")
            StartUpApp.instance.eventFlow.emit(StartUpApp.StartupEvent.STARTUP_FINISH)
            delay(ONE_SECOND * 5)   // 等待Launcher启动
            logTagD(TAG, "结束自己")
            stopSelf()
        }
    }

    /**
     * 执行初期设定任务
     */
    private suspend fun executeSetupTasks() {
        val failTaskList = mutableListOf<suspend () -> Unit>()
        taskList.forEach {
            try {
                logTagV(TAG, "执行任务:${it.name}")
                it.invoke()
            } catch (tr: Throwable) {
                logTagW(TAG, "执行任务失败:${it.name}", tr = tr)
                failTaskList.add(it)
            }
        }
        if (failTaskList.isNotEmpty()) {
            logTagW(TAG, "执行失败的任务:${failTaskList.size}")
            failTaskList.forEach {
                try {
                    logTagV(TAG, "执行失败的任务")
                    it.invoke()
                } catch (tr: Throwable) {
                    logTagW(TAG, "执行失败的任务失败!", tr = tr)
                }
            }
        }
    }

    /**
     * 启用Launcher
     */
    private suspend fun enableLauncher() = withContext(Dispatchers.IO) {

        logTagI(TAG, "启用Launcher")
        val componentName = ComponentName(
            "com.czur.starry.device.launcher",
            "com.czur.starry.device.launcher.pages.view.launcher.LauncherActivity"
        )
        packageManager.setComponentEnabledSetting(
            componentName,
            PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
            PackageManager.DONT_KILL_APP
        )

        // 检查是否已经启动了
        while (!checkAtyEnable(componentName)) {
            delay(100)
        }
        logTagI(TAG, "Launcher 已成功启用")
    }

    private fun checkAtyEnable(componentName: ComponentName): Boolean {
        return packageManager.getComponentEnabledSetting(componentName) == PackageManager.COMPONENT_ENABLED_STATE_ENABLED
    }


    /**
     * 禁用自己
     */
    private suspend fun disableSelf() = withContext(Dispatchers.IO) {
        logTagI(TAG, "禁用 StartUP")
        val bootComponentName =
            ComponentName(
                this@StartUpFinishSettingWindow,
                "com.czur.starry.device.startupsettings.BootActivity"
            )
        packageManager.setComponentEnabledSetting(
            bootComponentName,
            PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
            PackageManager.DONT_KILL_APP    // 这个Flag失效了?
        )

        // 检查是否已经启动了
        while (checkAtyEnable(bootComponentName)) {
            delay(100)
        }
        logTagI(TAG, "StartUP 已成功禁用")
    }

    /**
     * 延迟, 防止结束的过快UI不好看
     */
    private suspend fun delayFinish() {
        delay(DELAY_FINISH_TIME)
    }

    /**
     * 设定一些杂项
     */
    private suspend fun initOtherSettings() = withContext(Dispatchers.IO) {
        logTagD(TAG, "设定HDMI自动打开")
        setBooleanSystemProp(KEY_HDMI_AUTO_OPEN, true)
        // 记录当前版本号
        VersionUtil(StartUpApp.instance).saveCurrentVersionCode()
    }

    private suspend fun copyExplainFile() = withContext(Dispatchers.IO) {
        val authority = "com.czur.starry.device.file.share.ShareFileProvider"
        val res = contentResolver.update(
            authority,
            FileHandler.PATH_COPY_EXPLAIN_FILE,
            ContentValues()
        )
        logTagD(TAG, "copyExplainFile:${res}")
    }

    // 初始化无线投屏
    private suspend fun initScreenShare() = withContext(Dispatchers.IO) {
        logTagD(TAG, "初始化无线投屏")

        val eShareServerSDK = EShareServerSDK.getSingleton(this@StartUpFinishSettingWindow)
        // 1. 关闭悬浮窗
        logTagD(TAG, "关闭宜享悬浮窗")
        eShareServerSDK.isShowPinWindow = false
        logTagD(
            TAG,
            "设置设备名悬浮窗到Default:${SettingUtil.ShareScreenSetting.DEF_VALUE_ENABLE_NAME_ALERT_WIN}"
        )
        SettingUtil.ShareScreenSetting.setEnableNameAlertWin()
        // 2. 设置默认设备名称
        logTagV(TAG, "设置设备名称")
        setEShareName(eShareServerSDK, 5)// 最多重试5次
        // 3. 设置投屏码为8位数
        logTagV(TAG, "关闭投屏码")
        disableESharePinCode(eShareServerSDK, 5)// 最多重试5次
        // 5. 打开EShare
        logTagV(TAG, "打开EShare")
        eShareServerSDK.startEShareServer()
        // 6. 开启DLNA
        logTagV(TAG, "打开DLNA")
        eShareServerSDK.isDlnaEnable = true
        // 7. Miracast
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagV(TAG, "关闭Miracast")
            eShareServerSDK.miracastEnable = false
        } else {
            logTagV(TAG, "打开Miracast")
            eShareServerSDK.miracastEnable = true
        }
        // 8. 打开Chromecast
        logTagV(TAG, "打开Chromecast")
        eShareServerSDK.isChromecastEnable = true
        // 9. 写入SN
        logTagV(TAG, "写入SN")
        eShareServerSDK.setExtraInfo(Constants.SERIAL)
        // 10. AirPlay
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD){
            logTagV(TAG, "关闭AirPlay")
            eShareServerSDK.isClientAirPlayEnable = false
        } else {
            logTagV(TAG, "打开AirPlay")
            eShareServerSDK.isClientAirPlayEnable = true
        }
        logTagI(TAG, "无线投屏初始化完成")
    }

    private suspend fun setEShareName(eShareServerSDK: IEShareServerSDK, retryTimes: Int) {
        withContext(Dispatchers.IO) {
            if (retryTimes <= 0) {
                logTagW(TAG, "超过了重试的上限, 不再重试了")
                return@withContext
            }
            eShareServerSDK.setDeviceName(DEF_E_SHARE_NAME)
            val checkName = try {
                eShareServerSDK.deviceName
            } catch (exp: Exception) {
                logTagW(TAG, "获取eShareDeviceName失败", tr = exp)
                null
            }
            if (checkName != DEF_E_SHARE_NAME) {
                logTagW(TAG, "设置EShare名字失败,1s后重试")
                delay(ONE_SECOND)
                setEShareName(eShareServerSDK, retryTimes - 1)
            } else {
                logTagV(TAG, "设置eShare名称成功")
            }

        }
    }

    private suspend fun disableESharePinCode(eShareServerSDK: IEShareServerSDK, retryTimes: Int) {
        withContext(Dispatchers.IO) {
            if (retryTimes <= 0) {
                logTagW(TAG, "超过了重试的上限, 不再重试了")
                return@withContext
            }
            val checkPinCodeMode = try {
                logTagD(TAG, "设置PINCode Mode为Disable")
                eShareServerSDK.pinRefreshInterval = 0
                eShareServerSDK.pinCodeMode = PIN_CODE_MODE_DISABLE

                eShareServerSDK.pinCodeMode
            } catch (exp: Exception) {
                logTagW(TAG, "获取eShare PIN Code Mode 失败", tr = exp)
                -1
            }
            if (checkPinCodeMode != PIN_CODE_MODE_DISABLE) {
                logTagW(TAG, "设置EShare Pin Code Mode失败,1s后重试")
                delay(ONE_SECOND)
                disableESharePinCode(eShareServerSDK, retryTimes - 1)
            } else {
                logTagV(TAG, "设置EShare Pin Code Mode成功")
            }

        }
    }

    private suspend fun setCZURAppConfig() {
        logTagD(TAG, "设置成者APP的默认值")
        // 将Launcher设置到默认值
        SettingUtil.PersonalizationSetting.resetLauncherConfig()

        logTagD(TAG, "设置拾音器参数")
        SettingUtil.CameraAndMicSetting.setAudioAlgoNSLevel()
        SettingUtil.CameraAndMicSetting.setAudioAlgoAGCLevel()
    }

    /**
     * 这里设置壁纸是为了在启动Launcher时不会看见黑屏
     * Launcher的壁纸是由自己去管理的
     */
    private suspend fun setSystemWallpaper() {
        logTagD(TAG, "设置壁纸")
        withContext(Dispatchers.IO) {
            val wpm =
                getSystemService(Context.WALLPAPER_SERVICE) as WallpaperManager
            val srcBitmap = when (Constants.versionIndustry) {
                VersionIndustry.PARTY_BUILDING ->
                    BitmapFactory.decodeResource(resources, R.raw.app_background_party_building)

                else -> BitmapFactory.decodeResource(resources, R.raw.app_background)
            }
            wpm.setBitmap(srcBitmap)
            logTagD(TAG, "设置壁纸完成")
        }
    }


    /**
     * 保存当前版本号
     */
    private suspend fun saveVersion() = withContext(Dispatchers.IO) {
        val versionName = Constants.FIRMWARE_NAME
        setStringSystemProp(KEY_DEVICE_LAST_VERSION, versionName)
    }

    private suspend fun otherSystemSettings() = withContext(Dispatchers.IO) {
        logTagD(TAG, "设置其他系统设置")
        // 设置会议摄像头
        logTagD(TAG, "设置为: 智能人像画面")
        systemManger.setTrackMode(SystemManagerProxy.TrackMode.TRACK_MODE_VISION)
    }

    private suspend fun markSetupComplete() = withContext(Dispatchers.IO) {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.S) {
            logTagD(TAG, "标记初期设定已完成")
            Settings.Secure.putInt(contentResolver, "user_setup_complete", 1)
            Settings.Global.putInt(contentResolver, "device_provisioned", 1)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        exitProcess(0)
    }
}