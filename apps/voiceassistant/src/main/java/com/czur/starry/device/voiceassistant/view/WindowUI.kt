package com.czur.starry.device.voiceassistant.view

import android.content.Context
import android.content.Context.WINDOW_SERVICE
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.WAKE_UP_PLAY_VIDEO
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.isHidingView
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.isWakeupState
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.asrQueue
import com.czur.starry.device.voiceassistant.widget.AnimateExpandingView
import com.czur.starry.device.voiceassistant.widget.AnimatedImageBallView
import com.czur.starry.device.voiceassistant.widget.AnimatedImageCloudsView
import com.czur.starry.device.voiceassistant.widget.ChatTextView
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay

/**
 *  author : WangHao
 *  time   :2025/03/25
 */

private const val TAG = "WindowUI"

class WindowUI(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val eventHandler: CommandEventHandler
) {

    private val windowManager by lazy {
        context.getSystemService(WINDOW_SERVICE) as WindowManager
    }
    private lateinit var windowParams: WindowManager.LayoutParams
    private lateinit var floatingView: View
    private lateinit var sunView: ChatTextView
    private lateinit var cloudsView: AnimatedImageCloudsView
    private lateinit var ballView: AnimatedImageBallView
    private lateinit var backgroundView: AnimateExpandingView

    fun onCreate() {
        if (::floatingView.isInitialized.not()) {
            floatingView =
                LayoutInflater.from(context).inflate(R.layout.floating_voice_assistant, null)
            windowParams = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                else
                    WindowManager.LayoutParams.TYPE_PHONE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.TRANSLUCENT
            )
            windowParams.gravity = Gravity.TOP or Gravity.LEFT
            windowParams.x = 20
            windowParams.y = 80
            backgroundView =
                floatingView.findViewById(R.id.backgroundView)
            ballView =
                floatingView.findViewById(R.id.ballView)
            cloudsView =
                floatingView.findViewById(R.id.cloudsView)
            sunView =
                floatingView.findViewById(R.id.sunView)
        }
    }

    //显示悬浮窗UI
    fun show() {
        lifecycleOwner.launch {
            if (floatingView.isAttachedToWindow) {
                return@launch
            }
            windowManager.addView(floatingView, windowParams)
            delay(200)
            eventHandler.handleCommand(CommandEvent(WAKE_UP_PLAY_VIDEO))
            backgroundView.startAnimation()
            delay(500)//等待背景动画展出
            ballView.initAnimation(true)
            delay(100) //这个随机的
            cloudsView.initAnimation(true)
            cloudsView.setScaleYFactor()
            sunView.startTtsMessage(eventHandler)
        }
    }

    //退出悬浮窗UI
    fun hide() {
        lifecycleOwner.launch {
            isHidingView.set(true)
            sunView.stopAnimation()
            ballView.stopAnimation()
            cloudsView.stopAnimation()
            delay(450)//目前调整合适
            backgroundView.stopAnimation()
            delay(1000)
            if (floatingView.isAttachedToWindow) {
                windowManager.removeView(floatingView)
            }
            isHidingView.set(false)
            isWakeupState.set(false)
        }
    }

    fun addHereMessage(message: String) {
        sunView.addHereMessage(message)
    }

    fun restartTtsMessage() {
        sunView.restartTtsMessage(eventHandler)
    }

    fun setScaleYFactor(value: Float) {
        cloudsView.triggerWaveEffect( value)
    }

    fun startAddMessage() {
        sunView.startAsrMessage(eventHandler)
    }


}
