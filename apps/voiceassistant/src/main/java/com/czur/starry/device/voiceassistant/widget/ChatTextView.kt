package com.czur.starry.device.voiceassistant.widget

import android.animation.Animator
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.compat.ChangeIdStateCache.invalidate
import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableString
import android.text.style.LineHeightSpan
import android.util.AttributeSet
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.android.internal.util.IntPair.first
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.AUDIO_EVENT
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.TTS_FINISH_EVENT
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.asrQueue
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.ttsMessageQueue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 *  author : WangHao
 *  time   :2025/03/20
 */

private const val TAG = "ChatTextView"

@SuppressLint("AppCompatCustomView")
class ChatTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TextView(context, attrs, defStyleAttr) {

    private val handler = Handler(Looper.getMainLooper())
    private val firstLine = StringBuilder()
    private val secondLine = StringBuilder()
    private val currentStringBuilder = StringBuilder()
    private var firstLineText = ""
    private var secondLineText = ""
    private var lastSpeaker = Speaker.D
    private val maxLineLength = 13 // 每行的最大字符数
    private val delayHuman = 50L // 人机说话的延迟时间，单位毫秒
    private var delayDevice = 200L // 人机说话的延迟时间，单位毫秒
    private var speaker: Speaker = Speaker.D
    private val mutex = Mutex() // 协程互斥锁
    private var currentDisplayJob: Job? = null
    private var asrQueueJob: Job? = null
    private var TtsQueueJob: Job? = null

    // 计算单行最大宽度（基于13个汉字基准）
//    private val chineseSample = "汉".repeat(13)
    private val maxLineWidth = 200f//paint.measureText(chineseSample)
    private val maxDisplayWidth = 400f//maxLineWidth * 2 //390 最多显示26个字符（两行）
    private var lastFirstLine = "" // 记录上一次的第一行文本
    //人机说话者
    private enum class Speaker { D, P }

    // 显示几行
    private enum class CharState { SINGLE, DOUBLE }

    // 初始化补偿器
    private val compensator = DelayCompensator()
    private val NANO_TO_MILLI = 1_000_000L
    private inline fun nanoTime() = System.nanoTime()

    //预加载字幕
    fun addHereMessage(text: String) {
        speaker = Speaker.D
        lastSpeaker = speaker
        firstLine.clear()
        secondLine.clear()
        displayInLine(text)
    }

    private suspend fun addMessage(text: String, isHuman: Boolean = false) = mutex.withLock {
        // 更新要显示的文本
        speaker = if (isHuman) Speaker.P else Speaker.D
        if (lastSpeaker != speaker) {
            lastSpeaker = speaker
            lastFirstLine = ""
            firstLine.clear()
            secondLine.clear()
        }
        //添加空格保持英文单词间距
        val processedText = if (text.matches(Regex("^[a-zA-Z\\s]*$"))) "$text " else text
        currentDisplayJob?.cancel()
        currentDisplayJob = if (speaker == Speaker.D) {
            //设备文字叠加
            CoroutineScope(Dispatchers.Main).launch {
                displayTtsMessage(processedText)
            }
        } else {
            //人说话直接诶替换
            CoroutineScope(Dispatchers.Main).launch {
                displayInLine(processedText)
            }
        }

    }


    //添加ASR和预加载
    fun startAsrMessage(eventHandler: CommandEventHandler) {
        asrQueueJob?.cancel()
        asrQueueJob = MainScope().launch {
            val currentChannel = asrQueue.get()
            while (isActive) {
                val message = currentChannel.receive()
                addMessage(message.text, message.isHuman)
                eventHandler.handleCommand(CommandEvent(AUDIO_EVENT))
            }
        }
    }


    fun restartTtsMessage(eventHandler: CommandEventHandler) {
        TtsQueueJob?.cancel()
        startTtsMessage(eventHandler)
    }

    //添加Tts字幕
    fun startTtsMessage(eventHandler: CommandEventHandler) {
        TtsQueueJob = MainScope().launch {
            var timeAnchor = nanoTime() // 基于纳秒的锚点
            while (isActive) {
                val message = ttsMessageQueue.get().receive()
                addMessage(message.item.text, false)
                //通知给VoiceAssistantService 的resetCountdown方法
                eventHandler.handleCommand(
                    CommandEvent(
                        TTS_FINISH_EVENT,
                        isFinal = message.isFinal
                    )
                )
                logTagD(TAG, "message:$message")
                val processStart = nanoTime()// 计算理论持续时间
                val expectedDuration =
                    (message.item.endTime - message.item.beginTime) * NANO_TO_MILLI
                // 计算剩余等待时间
                val elapsed = (nanoTime() - processStart)
                val remainingNano = expectedDuration - elapsed
                val remainingMillis = remainingNano / NANO_TO_MILLI

                // 执行精准等待
                if (remainingMillis > 0) {
                    preciseDelay(remainingMillis)
                }

                // 更新锚点（误差隔离）
                timeAnchor = nanoTime()
            }
        }
    }

    //精准延迟
    private suspend fun preciseDelay(targetMillis: Long) {
        val nanoStart = nanoTime()
        val targetNano = targetMillis * NANO_TO_MILLI
        // 阶段1：常规delay（节省CPU）
        if (targetMillis > 20) {
            val compensation = compensator.getCompensation()
            delay(targetMillis - compensation * 3.toLong())
        }
        // 阶段2：自旋等待（高精度收官）
        while ((nanoTime() - nanoStart) < targetNano) {
            // 空循环消耗CPU但确保最后时刻精准
        }
        // 误差测量与反馈
        val actualMillis = (nanoTime() - nanoStart) / NANO_TO_MILLI
        val error = actualMillis - targetMillis
        compensator.update(error)
    }


    private fun displayTtsMessage(showText: String) {
        setTextColor(getTextColor())
        visibility = VISIBLE
        firstLine.append(showText)
        if (paint.measureText(firstLine.toString()) <= maxLineWidth) {
            lastFirstLine = firstLine.toString()
            if (secondLine.isNotEmpty()) {
                updateTextAndColor(CharState.DOUBLE)
            }else {
                updateTextAndColor(CharState.SINGLE)
            }
        }else {
            secondLine.clear().append(lastFirstLine)
            firstLine.clear().append(showText)
            updateTextAndColor(CharState.DOUBLE)
        }
    }

    private fun displayInLine(showText: String) {
        setTextColor(getTextColor())
        visibility = VISIBLE

        if (paint.measureText(showText) <= maxLineWidth) {
            firstLine.clear().append(showText)
            lastFirstLine = firstLine.toString()
            updateTextAndColor(CharState.SINGLE)
        }else if (paint.measureText(showText) <= maxDisplayWidth) {
            val (second, first) = splitToTwoLines(showText, maxLineWidth)
            firstLine.clear().append(first)
            secondLine.clear().append(second)
            updateTextAndColor(CharState.DOUBLE)
        }else {
            val displayText = findDisplayableSuffix(showText, maxDisplayWidth)
            val (second, first) = splitToTwoLines(displayText, maxLineWidth)
            firstLine.clear().append(first)
            secondLine.clear().append(second)
            updateTextAndColor(CharState.DOUBLE)
        }

    }

    /**
     * 从传入的文本末尾开始，截取宽度不超过 maxWidth 的后缀文本。
     *
     * @param text 输入的文本字符串。
     * @param maxWidth 允许的最大文本宽度。
     * @return 截取后的后缀文本。
     */
    private fun findDisplayableSuffix(text: String, maxWidth: Float): String {
        // 边界条件处理：如果文本为空或最大宽度小于等于 0，直接返回空字符串
        if (text.isEmpty() || maxWidth <= 0) return ""

        // 使用反向二分查找优化
        var low = 0
        var high = text.length
        var bestIndex = text.length // 默认不截断

        // 添加字符宽度缓存，用于存储每个字符位置对应的累计宽度
        val widthCache = mutableMapOf<Int, Float>().apply {
            put(0, 0f)
        }

        // 预计算累计宽度
        var accumulated = 0f
        text.forEachIndexed { i, c ->
            accumulated += paint.measureText(c.toString())
            widthCache[i + 1] = accumulated
        }

        // 主查找逻辑：反向二分查找合适的截取位置
        while (low <= high) {
            val mid = (low + high) / 2
            // 计算从 mid 位置到文本末尾的子文本宽度
            val subWidth = widthCache[text.length]!! - widthCache[mid]!!

            when {
                // 如果子文本宽度超过最大宽度，需要向右移动截取位置
                subWidth > maxWidth -> low = mid + 1
                // 否则，记录当前位置并向左移动，尝试找到更优的截取位置
                else -> {
                    bestIndex = mid
                    high = mid - 1
                }
            }
        }

        // 最终安全校验：确保截取位置不超过文本长度
        return text.substring(bestIndex.coerceAtMost(text.length))
    }


    // 核心算法2：智能分行
    private fun splitToTwoLines(text: String, lineWidth: Float): Pair<String, String> {
        // 边界条件处理
        if (text.isEmpty() || lineWidth <= 0) return Pair("", text)

        // 预计算字符宽度缓存
        val widthCache = FloatArray(text.length + 1).apply {
            for (i in 1..text.length) {
                this[i] = this[i - 1] + paint.measureText(text[i - 1].toString())
            }
        }

        // 二分查找最优分割点
        var low = 0
        var high = text.length
        var bestIndex = 0

        while (low <= high) {
            val mid = (low + high) / 2
            val currentWidth = widthCache[mid]

            when {
                currentWidth <= lineWidth -> {
                    bestIndex = mid
                    low = mid + 1
                }

                else -> high = mid - 1
            }
        }

        // 安全分割
        val firstLine = text.substring(0, bestIndex)
        val secondLine = text.substring(bestIndex)

        return Pair(firstLine, secondLine)
    }


    // 更新TextView的文本和颜色
    private fun updateTextAndColor(charState: CharState = CharState.SINGLE) {

        if (Looper.myLooper() != Looper.getMainLooper()) {
            logTagD(TAG, "===========Looper.getMainLooper():")
            post { updateTextAndColor(charState) }
            return
        }
        when (charState) {
            CharState.SINGLE -> {
                firstLineText = firstLine.toString()
                text = buildSpannable(firstLineText)
            }

            CharState.DOUBLE -> {
                firstLineText = firstLine.toString()
                secondLineText = secondLine.toString()
                text =  buildSpannableWithLineBreak(secondLineText,firstLineText)
            }

        }
    }

    private fun buildSpannable(text: String): Spannable {
        return SpannableString(text)
    }

    private fun buildSpannableWithLineBreak(line1: String, line2: String): Spannable {
        // 安全拼接逻辑
        val fullText = buildString {
            append(line1)
            if (line2.isNotEmpty()) append("\n")
            append(line2)
        }
        return SpannableString(fullText).apply {
            setSpan(LineHeightSpan.Standard(24), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }


    private fun getTextColor(): Int {
        return when (speaker) {
            Speaker.D -> Color.WHITE
            Speaker.P -> Color.parseColor("#97E5FF")
        }
    }

    fun startAnimation() {
        scaleX = 1f
        scaleY = 1f
        invalidate()
    }

    fun stopAnimation() {
        val scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f)
        val scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f)
        // 设置动画的持续时长为500毫秒
        scaleXAnimator.duration = 500
        scaleYAnimator.duration = 500
        // 为动画添加监听器，以便在动画结束时隐藏视图
        val animatorListener = object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
            }

            override fun onAnimationEnd(p0: Animator) {
                firstLineText = ""
                secondLineText = ""
                visibility = GONE
                scaleX = 1f
                scaleY = 1f
            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }

        }

        // 为缩放动画设置监听器
        scaleXAnimator.addListener(animatorListener)
        scaleYAnimator.addListener(animatorListener)

        scaleXAnimator.start()
        scaleYAnimator.start()
    }

    // 重写onDetachedFromWindow方法以在视图被销毁时停止动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
    }

}

// 维护误差补偿值（根据历史误差动态调整）
class DelayCompensator(
    private val windowSize: Int = 5,  // 滑动窗口大小
    private val initCompensation: Long = 20  // 初始补偿值
) {
    private val errorWindow = ArrayDeque<Long>()

    fun update(measuredError: Long) {
        if (errorWindow.size >= windowSize) {
            errorWindow.removeFirst()
        }
        errorWindow.addLast(measuredError)
    }

    fun getCompensation(): Long {
        return if (errorWindow.isEmpty()) initCompensation
        else errorWindow.average().toLong()
    }
}