package com.czur.starry.device.transcription.service

import android.annotation.SuppressLint
import android.audioai.AudioAiServiceCallback
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.transcription.Config.BIG_TRANS_HEIGHT
import com.czur.starry.device.transcription.Config.BIG_TRANS_HEIGHT_TEXT
import com.czur.starry.device.transcription.Config.BIG_TRANS_TEXT_Y
import com.czur.starry.device.transcription.Config.BIG_TRANS_WIDTH
import com.czur.starry.device.transcription.Config.BIG_TRANS_X
import com.czur.starry.device.transcription.Config.BIG_TRANS_Y
import com.czur.starry.device.transcription.Config.DEFAULT_SHOW_CONTENT
import com.czur.starry.device.transcription.Config.ERROR_FAILED_CODE_LIST
import com.czur.starry.device.transcription.Config.ERROR_USING_NO_TIME_LIST
import com.czur.starry.device.transcription.Config.GENERATE_MEETING_MINUTES
import com.czur.starry.device.transcription.Config.PREFERENCE_NAME
import com.czur.starry.device.transcription.Config.SHOW_CONTENT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TEXT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TRANS
import com.czur.starry.device.transcription.Config.SOURCE_LANG
import com.czur.starry.device.transcription.Config.TARGET_LANG
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.activity.AITransRenameDialogActivity
import com.czur.starry.device.transcription.manager.AudioAiManager
import com.czur.starry.device.transcription.model.AsrResult
import com.czur.starry.device.transcription.util.FrontEllipsizeHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


class SubtitleTransAlertWindowService : AlertWindowService() {
    private val TAG = "SubtitleTransAlertWindowService"

    private val sharedPreferences by lazy {
        getSharedPreferences(PREFERENCE_NAME, Context.MODE_PRIVATE)
    }

    private val sourceLanguage by lazy {
        sharedPreferences.getString(SOURCE_LANG, "CN")
    }

    private val targetLanguage by lazy {
        sharedPreferences.getString(TARGET_LANG, "EN")
    }

    private val showContent by lazy {
        sharedPreferences.getString(SHOW_CONTENT, DEFAULT_SHOW_CONTENT)
    }

    private val generateMeetingMinutes by lazy {
        sharedPreferences.getBoolean(GENERATE_MEETING_MINUTES, true)
    }

    private lateinit var screenLockReceiver: BroadcastReceiver

    // 网络状态监听
    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(this)
    }

    private val wakeupScreenLock by lazy {
        CZPowerManager.createOneWakeLock("aiTransWakeUpScreen")
    }

    // 是否显示网络错误提示
    private var isShowingNetworkError = false

    // 最后一次内容回调时间
    private var lastContentCallbackTime = System.currentTimeMillis()

    override val layoutId: Int
        get() = R.layout.float_text_layout

    //    override val windowWidth: Int
//        get() = BIG_TRANS_WIDTH
    override val windowWidth: Int
        get() = BIG_TRANS_WIDTH
    override val windowHeight: Int
        get() = BIG_TRANS_HEIGHT
    override val xOffSet: Int
        get() = BIG_TRANS_X
    override val yOffset: Int
        get() = BIG_TRANS_Y

    override val careKeyEvent: Boolean = false
    override val draggable: Boolean
        get() = true
    override val autoAdsorption: Boolean
        get() = false

    private val transcriptionCL: ConstraintLayout by ViewFinder(R.id.transcriptionCL)
    private val scaleWindowIv: ImageView by ViewFinder(R.id.scaleWindowIv)
    private val resultTv: TextView by ViewFinder(R.id.resultTv)
    private val transResultTv: TextView by ViewFinder(R.id.transResultTv)
    private val netErrorTv: TextView by ViewFinder(R.id.netErrorTv)

    private val asrResultList = mutableListOf<AsrResult>()

    private var lastX = BIG_TRANS_X
    private var lastY = BIG_TRANS_Y

    private var historyContent = ""
    private var currentContent = ""
    private var historyTrans = ""
    private var currentTrans = ""

    private var narrowToCircleJob: Job? = null

    private var lastTranslationTime = System.currentTimeMillis()

    // 记录开始翻译的时间
    private var startTranslationTime = System.currentTimeMillis()

    private var textInSmall = ""
    private var transInSmall = ""

    private val resultContentTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(resultTv, 3)
    }

    private val resultTransTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(transResultTv, 2)
    }

    // 内部回调类
    private val mAudioAiServiceCallback: AudioAiServiceCallback = object : AudioAiServiceCallback {


        override fun onAudioAsrResult(result: String, roleres: String) {
            logTagD("song", "onAudioAsrResult result:$result roleres:$roleres")
            makeDataText(false, result)
        }

        override fun onAudioTranslateResult(result: String, roleres: String) {
            logTagD("song", "onAudioTranslateResult result:$result roleres:$roleres")
            makeDataText(true, result)
        }

        override fun onAudioAiError(errorCode: Int, errorMsg: String) {
            logTagI(TAG, "onAudioAiError errorCode:$errorCode, errorMsg:$errorMsg")
            when (errorCode) {
                -500, -501, -502 -> {
                    //   java层返回的,不处理,直接和算法服务层的错误码对接
                }

                in ERROR_USING_NO_TIME_LIST -> {//时长不足
                    launch(Dispatchers.IO) {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            NoticeHandler.sendMessage(
                                MsgType(
                                    MsgType.COMMON,
                                    MsgType.COMMON_TOAST
                                )
                            ) {
                                put(getString(R.string.toast_ai_has_no_member_time))
                            }
                            stopTrans()

                        }
                    }
                    logTagI(TAG, "onAudioAiError 会员时长不足")
                }

                in ERROR_FAILED_CODE_LIST -> {//过程中的其他错误
                    launch(Dispatchers.IO) {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            NoticeHandler.sendMessage(
                                MsgType(
                                    MsgType.COMMON,
                                    MsgType.COMMON_TOAST
                                )
                            ) {
                                put(getString(R.string.toast_ai_error_in_using))
                            }
                            stopTrans()
                        }
                    }
                    logTagI(TAG, "onAudioAiError 过程中的其他错误")
                }

                else -> {

                }

            }

        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)

        if (showContent == SHOW_CONTENT_TEXT) {
            lastY = BIG_TRANS_TEXT_Y
        }
        // 登录状态监听
        UserHandler.isLoginLive.observe(this) {
            // 停止翻译,并刷新UI
            if (!it) {
                launch(Dispatchers.Main) {
                    toast(R.string.toast_ai_rename_success)
                }
                stopTrans(false)
            }
        }

        if (AudioAiManager.getServiceManager() == null) {
            AudioAiManager.init()
        }

        // 使用定义好的观察者变量
        TransHandler.stopTransLive.observe(this@SubtitleTransAlertWindowService) {
            if (it) {
                TransHandler.stopTrans = false
                stopTrans()
            }
        }

        TransHandler.showSubtitlesLive.observe(this@SubtitleTransAlertWindowService) {
            logTagI(TAG, "TransHandler.showSubtitlesLive observe $it")
            changeVis(it)
        }

        logTagI(TAG, "SubtitleTransAlertWindowService--getSystemService")
        AudioAiManager.registerCallback(mAudioAiServiceCallback);
        logTagI(TAG, "mAudioAiServiceManager--${AudioAiManager.getServiceManager()}")
        AudioAiManager.getAsrLangs()?.forEach {
            logTagI(TAG, "getAsrLangs--$it")
        }
        AudioAiManager.getTranslateOrigLangs()?.forEach {
            logTagI(TAG, "translateOrigLangs--$it")
        }
        AudioAiManager.getTranslateTargetLangs()?.forEach {
            logTagI(TAG, "translateTargetLangs--$it")
        }


        AudioAiManager.setTranslateLang(
            sourceLanguage!!, targetLanguage!!
        )
        //比如需要识别多个，那asr语言就传CN==EN==JP==XX \
        // 翻译的话是两个语言${sourceLanguage}==${targetLanguage}
        AudioAiManager.setAsrLang("${sourceLanguage}==${targetLanguage}")
        logTagI(
            TAG, "showContent--${showContent}  " +
                    "generateMeetingMinutes--$generateMeetingMinutes   " +
                    "sourceLanguage--$sourceLanguage   " +
                    "targetLanguage--$targetLanguage"
        )

        when (showContent) {
            SHOW_CONTENT_TEXT -> {// 实时字幕
                AudioAiManager.setTranslateEnabled(false)
            }

            SHOW_CONTENT_TRANS -> {//现场互译
                AudioAiManager.setTranslateEnabled(true)
            }
        }

        AudioAiManager.setSummaryEnabled(generateMeetingMinutes)
        AudioAiManager.startAsr()

        // 记录开始翻译的时间
        startTranslationTime = System.currentTimeMillis()
        lastContentCallbackTime = System.currentTimeMillis() // 初始化最后内容回调时间
        logTagI(TAG, "开始翻译，记录时间: $startTranslationTime")

        TransHandler.showSubtitles = true

        return START_NOT_STICKY // 不需要被kill后重建Service
    }

    override fun View.initViews() {
        // 设置固定行高，避免中英文切换时行高不一致导致的跳动问题
        (resultTv as com.czur.starry.device.transcription.util.FixedLineHeightTextView).apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 每行36px的高度
            includeFontPadding = false // 禁用字体内边距
        }

        (transResultTv as com.czur.starry.device.transcription.util.FixedLineHeightTextView).apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 与resultTv使用相同的行高
            includeFontPadding = false
        }

        scaleWindowIv.setOnClickListener { // 缩小到小窗
            TransHandler.showSubtitles = false
        }
    }

    override suspend fun initTask() {
        super.initTask()

    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun initData() {
        super.initData()
        // 初始化 ScreenLockReceiver
        screenLockReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_OFF -> {
                        TransHandler.stopTrans = true
                    }
                }
            }
        }
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_SCREEN_ON)
        }
        registerReceiver(screenLockReceiver, filter)

        // 启动网络状态监听
        netStatusUtil.startWatching()

        // 监听网络状态变化
        netStatusUtil.internetStatusLive.observe(this) { status ->
            logTagI(TAG, "网络状态变化: $status")
            if (status == InternetStatus.DISCONNECT) {
                // 网络断开，显示错误提示
                if (!isShowingNetworkError) {
                    makeDataText(false, "", false, true)
                }
            } else if (status == InternetStatus.CONNECT && isShowingNetworkError) {
                // 网络恢复，清除错误提示
                isShowingNetworkError = false
                makeDataText(false, "", true)
            }
        }

        // 启动无内容回调自动停止任务
        startNoContentCallbackWatchJob()
    }


    private fun startNoContentCallbackWatchJob() {
        launch(Dispatchers.IO) {
            while (true) {
                // 检查是否超过5分钟没有内容回调
                if ((System.currentTimeMillis() - lastContentCallbackTime) > (5 * ONE_MIN)) {
                    logTagI(TAG, "超过5分钟没有内容回调，自动停止")
                    stopTrans()
                    break
                } else if ((System.currentTimeMillis() - lastTranslationTime) > (10 * ONE_SECOND)) {
                    // 10秒没有新内容，清空文本并重置行数计数
                    makeDataText(false, "", true)
                }
                delay(2000) // 每2秒检查一次
            }
        }
    }

    override fun onDestroy() {
        wakeupScreenLock.release()

        unregisterReceiver(screenLockReceiver)
        TransHandler.isTranslating = false

        // 停止网络状态监听
        netStatusUtil.stopWatching()

        super.onDestroy()
    }

    fun makeDataText(
        isEndOfASentence: Boolean,
        content: String,
        clearText: Boolean = false,
        isNetworkError: Boolean = false
    ) {
        //每次调用确保息屏timeout重新计时
        wakeupScreenLock.acquire()
        lastTranslationTime = System.currentTimeMillis()
        logTagI(
            TAG,
            " makeDataText-content--$content isEndOfASentence--$isEndOfASentence clearText--$clearText isNetworkError--$isNetworkError"
        )

        // 如果不是清空文本或网络错误，更新最后内容回调时间
        if (!clearText && !isNetworkError && content.isNotEmpty()) {
            lastContentCallbackTime = System.currentTimeMillis()
        }

        launch(Dispatchers.IO) {
            currentContent = ""
            currentTrans = ""
            if (isNetworkError) {
                isShowingNetworkError = true
            }

            if (isShowingNetworkError) {
                // 显示网络错误提示
                withContext(Dispatchers.Main) {
                    netErrorTv.show()
                    // 重置行数计数，清除补全记录
                    resultContentTvHelper.resetLineCount()
                    resultTransTvHelper.resetLineCount()

                    resultContentTvHelper.setText("", "", false)
                    resultTransTvHelper.setText("", "", false)
                    historyContent = ""
                    historyTrans = ""

                }
                return@launch
            }

            withContext(Dispatchers.Main) {
                netErrorTv.gone()
            }

            val parts = content.split("=====", limit = 2)
            val currentContent = parts.getOrElse(0) { "" }
            var currentTrans = parts.getOrElse(1) { "" }

            if (showContent == SHOW_CONTENT_TEXT) {
                currentTrans = ""
                historyTrans = ""
            }

            withContext(Dispatchers.Main) {
                when {
                    TransHandler.showSubtitles == false
                            || clearText -> {
                        // 清空文本时重置行数计数，清除补全记录
                        resultContentTvHelper.resetLineCount()
                        resultTransTvHelper.resetLineCount()
                        // 直接设置空文本，避免使用setText方法可能添加的空白行
                        resultTv.text = ""
                        transResultTv.text = ""
                        historyContent = ""
                        historyTrans = ""
                    }

                    isEndOfASentence -> {
                        historyContent =
                            resultContentTvHelper.setText(historyContent, currentContent, true)
                        historyTrans = resultTransTvHelper.setText(historyTrans, currentTrans, true)
                    }

                    else -> {
                        historyContent =
                            resultContentTvHelper.setText(historyContent, currentContent, false)
                        historyTrans =
                            resultTransTvHelper.setText(historyTrans, currentTrans, false)

                    }
                }
            }
        }
    }

    private fun changeVis(vis: Boolean) {
        logTagI(
            TAG,
            "changeVis $vis lastX $lastX lastY $lastY"
        )
        if (vis) {
            if (transcriptionCL.width == BIG_TRANS_WIDTH) {
                logTagI(TAG, "ai互译字幕已显示,return")
                return
            }
            val width = BIG_TRANS_WIDTH
            var height = 0
            if (showContent == SHOW_CONTENT_TEXT) {
                height = BIG_TRANS_HEIGHT_TEXT
            } else {
                height = BIG_TRANS_HEIGHT
            }

            refreshParams(width, height, lastX, lastY)
        } else {
            if (transcriptionCL.width == 1) {
                logTagI(TAG, "ai互译字幕已隐藏,return")
                return
            }
            makeDataText(false, "", true)
            saveCurrentAndRefreshParams()
            refreshParams(1, 1, 1, 1)
        }
    }

    private fun stopTrans(isLogin: Boolean = true) {
        TransHandler.isTranslating = false
        logStackTrace("song")
        val sessionId = AudioAiManager.getSessionId()
        logTagI(TAG, "00getSessionId--$sessionId")
        AudioAiManager.stopAsr(mAudioAiServiceCallback)
        logTagI(TAG, "11getSessionId--$sessionId")

        AudioAiManager.release()
        logTagI(TAG, "22getSessionId--$sessionId")

        // 格式化开始时间为年月日时分秒
        val sdf = SimpleDateFormat("yyyyMMddHHmm", Locale.getDefault())
        val formattedTime = sdf.format(Date(startTranslationTime))
        logTagI(TAG, "停止翻译，使用开始时间作为默认文件名: $formattedTime")

        if (isLogin) {
            val intent = Intent(this, AITransRenameDialogActivity::class.java).apply {
                putExtra("sessionId", sessionId)
                putExtra("defaultFileName", formattedTime)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
        }

        stopSelf()
    }

    private fun saveCurrentAndRefreshParams() {
        lastX = wmParams!!.x
        lastY = wmParams!!.y
    }
}