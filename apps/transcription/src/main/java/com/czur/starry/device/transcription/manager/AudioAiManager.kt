package com.czur.starry.device.transcription.manager

import android.audioai.AudioAiServiceCallback
import android.audioai.AudioAiServiceManager
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.data.provider.UserHandler

object AudioAiManager {
    private const val TAG = "AudioAiManager"
    private val audioAiServiceManager: AudioAiServiceManager by lazy(LazyThreadSafetyMode.NONE) {
        (globalAppCtx.getSystemService("audioai") as AudioAiServiceManager)
    }

    fun init() {
        audioAiServiceManager.init(UserHandler.accountNo)
    }

    fun release() {
        logTagI(TAG, "release")
        audioAiServiceManager.unInit()

    }

    fun getServiceManager(): AudioAiServiceManager? = audioAiServiceManager

    fun registerCallback(callback: AudioAiServiceCallback) {

        audioAiServiceManager.registerCallback(callback)
        logTagI(TAG, "registerCallback $callback")
    }

    fun unregisterCallback(callback: AudioAiServiceCallback) {
        audioAiServiceManager.unregisterCallback(callback)
    }

    fun startAsr() {

        audioAiServiceManager.startAsr()
    }

    fun stopAsr(mAudioAiServiceCallback: AudioAiServiceCallback? = null) {
        if (mAudioAiServiceCallback != null) {
            unregisterCallback(mAudioAiServiceCallback)
        }
        audioAiServiceManager.stopAsr()
        logTagI(TAG, "stopAsr")
    }

    fun setTranslateEnabled(enabled: Boolean) {
        audioAiServiceManager.setTranslateEnabled(enabled)
        logTagI(TAG, "setTranslateEnabled $enabled")
    }

    fun setTranslateLang(sourceLang: String, targetLang: String) {
        audioAiServiceManager.setTranslateLang(sourceLang, targetLang)
        logTagI(TAG, " setTranslateLang sourceLang $sourceLang targetLang $targetLang")
    }

    fun setAsrLang(lang: String) {
        audioAiServiceManager.setAsrLang(lang)
        logTagI(TAG, "setAsrLang $lang")
    }

    fun setSummaryEnabled(enabled: Boolean) {
        audioAiServiceManager.setSummaryEnabled(enabled)
        logTagI(TAG, "setSummaryEnabled $enabled")

    }

    fun getSessionId(): String? {
        return audioAiServiceManager.sessionId
    }
    

    fun getAsrLangs(): MutableList<String>? = audioAiServiceManager.asrLangs

    fun getTranslateOrigLangs(): MutableList<String>? = audioAiServiceManager.translateOrigLangs

    fun getTranslateTargetLangs(): MutableList<String>? =
        audioAiServiceManager.translateTargetLangs
}