<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/quickBootLT"
    tools:ignore="PxUsage"
    tools:layout_height="280px"
    tools:layout_width="280px">

    <ImageView
        android:id="@+id/quickBootBgIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:src="@drawable/icon_quickboot_notice_msg" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30px"
        android:orientation="vertical"
        app:constraint_referenced_ids="shareNameTv,wifiNameLl"
        app:flow_verticalGap="5px"
        app:layout_constraintBottom_toTopOf="@id/quickBootTitleTv" />

    <TextView
        android:id="@+id/shareNameTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="left"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="bold"
        android:visibility="gone"
        tools:text="StarryHub-市场" />

    <LinearLayout
        android:id="@+id/wifiNameLl"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="5px"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/quickBootTitleTv">

        <ImageView
            android:id="@+id/wifiNameIv"
            android:layout_width="20px"
            android:layout_height="14px"
            android:src="@drawable/ic_quick_boot_share_wifi" />

        <TextView
            android:id="@+id/wifiNameTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginLeft="7px"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/white"
            android:textSize="20px"
            android:textStyle="bold"
            tools:text="CZUR-5G" />

        <TextView
            android:id="@+id/ethernetTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:text="@string/quick_boot_screen_share_ethernet"
            android:textColor="@color/white"
            android:textSize="20px"
            android:textStyle="bold"
            android:visibility="gone" />
    </LinearLayout>

    <TextView
        android:id="@+id/quickBootContent"
        style="@style/short_cut_app_content"
        app:layout_constraintBottom_toTopOf="@+id/quickBootTitleTv"
        app:layout_constraintLeft_toLeftOf="@+id/quickBootTitleTv"
        android:maxWidth="200px"
        android:ellipsize="middle"
        android:visibility="gone"
        android:singleLine="true"
        tools:text="未读" />

    <FrameLayout
        android:id="@+id/quickBootBadgePoint"
        android:layout_marginLeft="3px"
        android:layout_width="12px"
        android:layout_height="12px"
        android:visibility="gone"
        app:layout_constraintLeft_toRightOf="@+id/quickBootContent"
        app:layout_constraintTop_toTopOf="@id/quickBootContent">

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="12px"
            android:layout_height="12px"
            android:layout_gravity="center"
            app:circleColor="#FFFFFF" />

        <com.czur.starry.device.baselib.widget.CircleView
            android:layout_width="10px"
            android:layout_height="10px"
            android:layout_gravity="center"
            app:circleColor="#ff5b5c" />
    </FrameLayout>

    <TextView
        android:id="@+id/quickBootTitleTv"
        style="@style/short_cut_app_tv"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="文件" />

    <ImageView
        android:id="@+id/quickBootBadgeIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:src="@drawable/ic_quick_boot_new_badge"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="@id/quickBootBgIv"
        app:layout_constraintTop_toTopOf="@id/quickBootBgIv" />

    <TextView
        android:id="@+id/fileCodeTitleTv"
        style="@style/file_code_title_tv"
        android:text="@string/quick_boot_file_code"
        android:visibility="gone"
        android:gravity="right"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/fileCodeContentTv"
        style="@style/file_code_content_tv"
        android:visibility="gone"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/fileCodeTitleTv" />

</merge>