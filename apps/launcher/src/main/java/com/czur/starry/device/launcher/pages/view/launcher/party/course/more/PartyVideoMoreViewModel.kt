package com.czur.starry.device.launcher.pages.view.launcher.party.course.more

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.launcher.data.bean.PartyVideoEntity
import com.czur.starry.device.launcher.net.IPartyVideoServer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

private const val TAG = "PartyVideoMoreViewModel"
const val QUANTITY_PER_PAGE = 4 // 每页显示4个item

class PartyVideoMoreViewModel(application: Application) : AndroidViewModel(application) {
    private val videoServer: IPartyVideoServer by lazy {
        HttpManager.getService()
    }

    val currentPageLive = DifferentLiveData(1)
    var currentPage: Int by LiveDataDelegate(currentPageLive)

    // 总页数
    val sumPageCountLive = DifferentLiveData<Int>()
    var sumPageCount: Int by LiveDataDelegate(sumPageCountLive, 1)

    /**
     * 加载每页的数据, 数量不足, 会填充到[QUANTITY_PER_PAGE]
     */
    suspend fun loadPartyVideoWithPlaceHolder(page: Int): PartyVideoEntity =
        withContext(Dispatchers.IO) {
            logTagV(TAG, "加载Launcher上的视频数据")
            val videoMiaoEntity = videoServer.getPartyFiles(page, QUANTITY_PER_PAGE)
            if (videoMiaoEntity.isSuccess) {
                videoMiaoEntity.body.also {
                    sumPageCount = calculateNumberPages(it.total)// 更新最大页码数量
                }
            } else {
                logTagW(TAG, "请求党建课程失败${videoMiaoEntity.code} - ${videoMiaoEntity.msg}")
                PartyVideoEntity.mkEmptyData()
            }
        }

    /**
     * 计算总页数
     */
    fun calculateNumberPages(totalSize: Int) =
        (totalSize + QUANTITY_PER_PAGE - 1) / QUANTITY_PER_PAGE
}