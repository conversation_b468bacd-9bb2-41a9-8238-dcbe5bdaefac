package com.czur.starry.device.file.view.localmeeting

import android.os.Bundle
import android.os.FileObserver
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.doOnItemLongClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.file.R
import com.czur.starry.device.file.base.RefreshAble
import com.czur.starry.device.file.bean.FileEntity
import com.czur.starry.device.file.databinding.FragmentFilePadBinding
import com.czur.starry.device.file.exp.CloudFileInUserExp
import com.czur.starry.device.file.exp.NoSpaceException
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.manager.FileShowInfoManager
import com.czur.starry.device.file.manager.LocalInfoManager
import com.czur.starry.device.file.manager.LocalMeetingAccess
import com.czur.starry.device.file.manager.transfer.FileTransfer
import com.czur.starry.device.file.utils.KEY_DURATION
import com.czur.starry.device.file.utils.TargetFileObserver
import com.czur.starry.device.file.utils.open
import com.czur.starry.device.file.view.IControlBar
import com.czur.starry.device.file.view.IFilePad
import com.czur.starry.device.file.view.dialog.CopyFileDialog
import com.czur.starry.device.file.view.dialog.ProgressDialog
import com.czur.starry.device.file.view.dialog.RenameDialog
import com.czur.starry.device.file.view.dialog.showCopyFileRepeatDialog
import com.czur.starry.device.file.view.dialog.showCopyNoSpaceDialog
import com.czur.starry.device.file.view.dialog.showHintDialog
import com.czur.starry.device.file.view.dialog.showOtherCopyError
import com.czur.starry.device.file.view.vm.MainViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Stack
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Created by 陈丰尧 on 2022/8/9
 * 本地会议录像页面
 */
class LocalMeetingListFragment : CZViewBindingFragment<FragmentFilePadBinding>(), IFilePad,
    RefreshAble {
    companion object {
        private const val TAG = "LocalMeetingListFragment"
    }

    private val loadingDialog by lazy {
        LoadingDialog().apply {
            showAtLeast = Lifecycle.State.STARTED
        }
    }
    private var progressDialog: ProgressDialog? = null

    // 记录是否正在刷新页面
    private val refreshing: AtomicBoolean = AtomicBoolean(false)

    private lateinit var controlBar: IControlBar

    override var copyJob: Job? = null
    override fun getBelongTo(): AccessType = AccessType.LOCAL_MEETING

    private val access = LocalMeetingAccess

    private val adapter = LocalMeetingAdapter()
    private var isSelMode: Boolean = false  // 是否是选择模式

    private val targetFileObserver: TargetFileObserver by lazy {
        createRootFileWatch()
    }

    private val pathStack = Stack<FileEntity>()

    private val isDeleting = AtomicBoolean(false)


    private var doCopyFileTotal = 0
    private var isNeedLoading: Boolean = true

    private var isOpeningAudioOrVideo: Boolean = false  // 正在打开音视频文件

    private val mainViewModel: MainViewModel by activityViewModels()

    override fun FragmentParams.initFragmentParams() {
        this.viewLifecycleObserver = object : AutoRemoveLifecycleObserver {

            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                logTagD(TAG, "开始监听文件变化")
                targetFileObserver.startWatch()

            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                logTagD(TAG, "停止监听文件变化")
                targetFileObserver.stopWatch()
            }

            override fun onResume(owner: LifecycleOwner) {
                super.onResume(owner)
                logTagD(TAG, "LocalMeetingListFragment - onResume")
                isOpeningAudioOrVideo = false
                if (isVisible) {
                    isNeedLoading = false
                    refresh()   // 页面可见才去刷新
                }
            }

        }
    }


    override fun FragmentFilePadBinding.initBindingViews() {
        filePadRv.layoutManager = LinearLayoutManager(requireContext())
        filePadRv.adapter = adapter
        filePadRv.closeDefChangeAnimations()
        filePadRv.setEmptyView()

        // 点击事件
        filePadRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            when (view.id) {
                else -> {
                    isSelMode
                        .yes {
                            adapter.changeSel(pos)
                            controlBar.updateSelectCount(adapter.selectCount, adapter.itemCount)
                        }.otherwise {
                            // 播放
                            if (isOpeningAudioOrVideo) {
                                logTagV(TAG, "正在打开音视频文件, ignore")
                                return@otherwise
                            }
                            val fileEntity = adapter.getData(pos)
                            isOpeningAudioOrVideo =
                                fileEntity.fileType == FileType.AUDIO || fileEntity.fileType == FileType.VIDEO
                            launch {
                                logTagV(TAG, "启动播放页面")
                                fileEntity.open(adapter.getDataList(), belong = getBelongTo())
                            }
                        }
                }
            }
            true
        }

        // 长按事件
        filePadRv.doOnItemLongClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            if (pos < 0) {
                return@doOnItemLongClick true
            }

            // 进入选择模式并选中当前项
            if (!isSelMode) {
                controlBar.enterSelMode()
                adapter.changeSel(pos)
                controlBar.updateSelectCount(adapter.selectCount, adapter.itemCount)
            }
            true
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        pathStack.push(access.getRootEntity())
    }

    /**
     * 调整排序方式
     */
    override fun sortBy(sortType: FileAccess.SortType) {
        refresh(true)
    }

    override fun setControlBar(iControlBar: IControlBar) {
        this.controlBar = iControlBar
    }

    /**
     * 切换选择模式
     */
    override fun changeUIMode(selectMode: Boolean) {
        this.isSelMode = selectMode
        adapter.selectMode = selectMode
        if (!isSelMode) {
            adapter.selectNone()
        }
    }

    /**
     * 全选
     */
    override fun selectAll() {
        adapter.selectAll()
        controlBar.updateSelectCount(adapter.selectCount, adapter.itemCount)
    }

    /**
     * 全不选
     */
    override fun selectNone() {
        adapter.selectNone()
        controlBar.updateSelectCount(0, adapter.itemCount)
    }

    /**
     * 删除选中文件
     */
    override suspend fun deleteSelect() {
        // 显示Loading
        loadingDialog.showDelay()
        isDeleting.set(true)

        val selFiles = adapter.getSelFiles()
        selFiles.isEmpty()
            .yes {
                // 没有选中任何文件
                toast(R.string.toast_choose_no_file)
            }.otherwise {
                // 执行删除操作
                when (val res = access.delFiles(selFiles)) {
                    FileAccess.Result.SUCCESS -> {
                        logTagV(TAG, "删除成功")
                        adapter.delSelect(selFiles) // 不重新刷新页面, 只更新UI, 这样快一些
                        isDeleting.set(false)
                    }

                    else -> {
                        logTagD(TAG, "删除文件失败${res}")
                        isDeleting.set(false)
                        refresh()
                    }
                }
            }
        // 取消loading
        loadingDialog.dismissImmediate()
    }

    override fun renameSelect() {
        super.renameSelect()
        val selFile = adapter.getSelFiles().first()
        RenameDialog(
            hint = getString(R.string.dialog_rename_hint),
            inputText = selFile.name.substringBeforeLast("."),
            title = getString(R.string.dialog_rename_title),
            isConstraintLength = true,
            limitLength = 50,   // 限制50个字符(22.9.6 刘志立)
        ) { newName ->
            when {
                newName == selFile.name.substringBeforeLast(".") -> {
                    logTagI(TAG, "文件名没有任何改变")
                }

                newName.isNotBlank() -> {
                    // 执行重命名
                    doRename(newName, selFile)
                }

                else -> {
                    // 没有输入文件名对话框
                    showHintDialog(getString(R.string.str_info_no_file_name))
                }
            }
        }.show()
    }

    /**
     * 执行重命名操作
     */
    private fun doRename(newName: String, selFile: FileEntity) {
        launch {
            logTagV(TAG, "重命名,newName:${newName}")
            loadingDialog.showDelay()
            // 为文件名添加扩展名
            var name = newName
            if (selFile.extension.isNotBlank()) {
                name += ".${selFile.extension}"
            }

            val result = access.renameFile(selFile, name, access.getRootEntity())
            if (result == FileAccess.Result.SUCCESS) {
                // 重新更新UI
                logTagV(TAG, "重命名成功, 刷新")
                refresh()   // 和刷新的Loading 弄到一起
            } else {
                logTagW(TAG, "重命名失败:${result}")
                loadingDialog.dismissImmediate()
                showErrorDialog(result)
            }
        }
    }

    private fun showErrorDialog(error: FileAccess.Result) {
        when (error) {
            FileAccess.Result.FILE_NAME_REPETITION ->
                // 文件名重复对话框
                showHintDialog(getString(R.string.str_info_filename_repeat))

            FileAccess.Result.SYSTEM_ERR ->
                showHintDialog(getString(R.string.str_info_system_err))

            else -> {}
        }
    }


    /**
     * 刷新
     */
    override fun refresh(needMoveToTop: Boolean) {
        if (refreshing.get()) {
            logTagW(TAG, "正在刷新,ignore")
            return
        }
        if (isDeleting.get()) {
            logTagW(TAG, "正在删除,ignore")
            return
        }

        logTagV(TAG, "刷新本地会议路径")
        launch {
            refreshing.set(true)
            if (isNeedLoading) {
                loadingDialog.showDelay()
            }
            val dataList = access.getItemsByTarget(
                access.getRootEntity(),
                sortType = FileShowInfoManager.loadSortType(getBelongTo())
            )
            val delFiles = mutableListOf<FileEntity>()
            val showDataList = dataList.filterNot {
                if (it.extension == "txt") {
                    return@filterNot false
                }
                val duration = it.getAttribute(KEY_DURATION).toLong()
                // 是00:00:00就从datalist移除
                if (duration == 0L) {
                    delFiles.add(it)
                    true
                } else {
                    false
                }
            }
            access.delFiles(delFiles)
            adapter.setData(showDataList)
            refreshing.set(false)
            loadingDialog.dismiss()
            //去除当前tab红点
            FileHandlerLive.fileTabUpdate = mainViewModel.currentTabKey
            isNeedLoading = true
        }
    }

    /**
     * 复制文件
     */
    override fun copyFiles() {
        super.copyFiles()
        logTagV(TAG, "复制选中的会议录像")
        val selFiles = adapter.getSelFiles()
        CopyFileDialog.Builder(selFiles)
            .setOnConfirmListener { path, moveMode ->
                doCopy(path, selFiles, moveMode)
            }
            .buildAndShow()
    }

    /**
     * 移动文件
     */
    override fun moveFiles() {
        super.moveFiles()
        logTagV(TAG, "移动选中的会议录像")
        val selFiles = adapter.getSelFiles()
        CopyFileDialog.Builder(selFiles, isMove = true)
            .setOnConfirmListener { path, moveMode ->
                doCopy(path, selFiles, moveMode)
            }
            .buildAndShow()
    }

    /**
     * 执行复制操作
     */
    private fun doCopy(
        targetDir: FileEntity,
        srcFiles: List<FileEntity>,
        isMove: Boolean,
    ) {
        launch {
            doCopyFileTotal = srcFiles.size
            copyJob = launch {
                access.getTransfer(targetDir, srcFiles, isMove)
                    .apply {
                        progressListener = { progressDialog?.progress = it }
                        needUserListener = ::onNeedUserHandle
                        finishedListener = { onCopyFinish(isMove) }
                        onErrorListener = {
                            // 先刷新一次当前文件夹
                            onCopyFinish(isMove)
                            when (it) {
                                is NoSpaceException -> showCopyNoSpaceDialog(it)
                                is CloudFileInUserExp -> toast(R.string.toast_conflict)
                                else -> showOtherCopyError(isMove)
                            }
                        }
                    }
                    .transfer()
            }

            // 显示进度对话框
            val progressDialogBuilder = ProgressDialog.Builder()
            if (isMove) {
                progressDialogBuilder.setTitle(R.string.str_dialog_progress_title_move)
            } else {
                progressDialogBuilder.setTitle(R.string.str_dialog_progress_title_copy)
            }
            progressDialog = progressDialogBuilder
                .setOnCancelListener {
                    logTagD(TAG, "取消复制/移动协程")
                    copyJob?.cancel() // 取消复制
                    doCopyFinishRefresh(isMove)
                }
                .buildAndShow()
        }
    }

    /**
     * 当复制/移动时发生错误的回调
     */
    private fun onNeedUserHandle(
        file: FileEntity,
        handleType: FileTransfer.NeedUserHandleType,
        mode: (FileTransfer.HandleMode) -> Unit,
    ) {
        // 接收错误信息
        when (handleType) {
            FileTransfer.NeedUserHandleType.FILE_NAME_REPEAT -> {
                showCopyFileRepeatDialog(file, mode)
            }
        }

    }


    /**
     * 当复制/移动完成后的回调
     * @param isMove :  是否是移动
     */
    private fun onCopyFinish(isMove: Boolean) {
        logTagV(TAG, "onCopyFinish")
        // 当完成时
        progressDialog?.let {
            logTagD(TAG, "进度对话框消失")
            it.dismissAllowingStateLoss()
            progressDialog = null
            doCopyFinishRefresh(isMove)
        }
    }

    private fun doCopyFinishRefresh(isMove: Boolean) {
        // 移动操作
        if (isMove) {
            logTagV(TAG, "移动完成, 刷新当前页面")
            launch {
                // 刷新当前文件夹 延迟1s, 来让存储容量稳定
                delay(ONE_SECOND)
                refresh()
            }
            return
        }

        // 复制操作, 切目的地是当前页面
        // 因为不支持 在当前页面中, 直接进行复制操作, 所以, 只需要刷新存储空间即可
        launch {
            // 刷新当前文件夹 延迟1s, 来让存储容量稳定
            delay(ONE_SECOND)
            access.updateUsage()
        }

    }

    private fun createRootFileWatch(): TargetFileObserver {
        return TargetFileObserver(
            access.getRootEntity().absPath,
            FileObserver.DELETE or FileObserver.CLOSE_WRITE or FileObserver.ATTRIB
        ) { event, path ->
            logTagV(TAG, "文件发生变化, event:${event}, path:${path}")
            if (event == FileObserver.DELETE) {
                logTagD(TAG, "文件被删除:$path")
                // 文件删除时不需要重新刷新页面, 更新UI的操作,在删除操作中已经完成
                LocalInfoManager.refreshUsageOnce() // 更新存储空间
                return@TargetFileObserver
            }
            // 只有在根路径才刷新
            if (pathStack.size > 1) {
                return@TargetFileObserver
            }
            launch {
                if (isDeleting.get()) {
                    return@launch
                }

                if (doCopyFileTotal <= 1) {
                    logTagD(TAG, "文件发生变化, 刷新页面")
                    delay(500)
                    if (event == FileObserver.CLOSE_WRITE) {
                        isNeedLoading = false
                    }
                    refresh(false)
                }
                doCopyFileTotal--

            }
        }
    }

}