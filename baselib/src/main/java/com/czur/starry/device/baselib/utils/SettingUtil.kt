package com.czur.starry.device.baselib.utils

import android.content.ContentResolver
import android.content.Context
import android.content.Intent
import android.database.ContentObserver
import android.media.AudioManager
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_DEVICE_LAUNCHER_MEETING_ROOM_NAME
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 3/4/21
 */
object SettingHandler {
    private val resolver by lazy {
        CZURAtyManager.appContext.contentResolver
    }

    const val AUTHORITY = "com.czur.starry.device.settings.czurprovider"

    const val SETTING_VALUES = "settingValues"
    const val SETTING_VALUES_CODE = 2
    const val COLUMN_NAME = "valueColumn"
    private val SETTINGS_URI = Uri.parse("content://${AUTHORITY}/${SETTING_VALUES}")

    const val SETTING_LANGUAGE = "language"

    // 语言: 英文 en
    val languageCode: String
        get() = getValue(SETTING_LANGUAGE)

    /**
     * 成者的语言设定
     * 只有简中,繁中和英文
     * 如果被使用其他方式修改了,默认为简中
     */
    val czurLang: CZURLang
        get() {
            return when (languageCode.lowercase()) {
                "en" -> CZURLang.EN
                "zh_tw" -> CZURLang.TW
                "ja" -> CZURLang.JP
                "ru" -> CZURLang.RU
                "it" -> CZURLang.IT
                "de" -> CZURLang.DE
                "ko" -> CZURLang.KO
                "fr" -> CZURLang.FR
                "es" -> CZURLang.ES
                else -> CZURLang.CN
            }
        }


    private fun getValue(key: String): String {
        val cursor = this.resolver.query(SETTINGS_URI, null, key, null, null)
        var result: String
        cursor.use {
            cursor?.moveToFirst()
            val index = cursor?.getColumnIndex(COLUMN_NAME) ?: -1
            if (index < 0) return ""
            result = cursor?.getString(index) ?: ""
        }
        return result
    }

    /**
     * @param webCode: 给web页面用的参数
     * @param serverCode: 给后台用的参数
     */
    enum class CZURLang(val webCode: String, val serverCode: String) {
        CN("zh", "zh_CN"), // 简中
        EN("en", "en_US"), // 英文
        TW("zht", "zh_TW"),  // 繁体中文
        JP("jp", "ja_JP"), // 日语
        RU("ru", "ru_RU"), // 俄语
        IT("it", "it_IT"), // 意大利语
        DE("de", "de_DE"), // 德语
        KO("ko", "ko_KR"), // 韩语
        FR("fr", "fr_FR"), // 法语
        ES("es", "es_ES") // 西班牙语
    }


}

object SettingUtil {
    private const val VALUE_BOOL_TRUE = 1
    private const val VALUE_BOOL_FALSE = 0

    private val observerHandler = Handler(Looper.getMainLooper())


    val cr: ContentResolver by lazy {
        CZURAtyManager.appContext.contentResolver
    }

    object EmptySetting : CZURSetting() {
        override val settingPrefix: String = ""
    }

    object AppStoreSetting : CZURSetting() {
        override val settingPrefix: String
            get() = "appStore"

        private const val KEY_REMIND_INSTALL_APP = "remindInstallApp" // 是否提醒安装应用
        suspend fun isRemindInstallApp(): Boolean = withContext(Dispatchers.IO) {
            getValue(
                KEY_REMIND_INSTALL_APP,
                Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas
            )
        }

        suspend fun setRemindInstallApp(remind: Boolean) = withContext(Dispatchers.IO) {
            setValue(KEY_REMIND_INSTALL_APP, remind)
        }

    }

    object ShareScreenSetting : CZURSetting() {
        override val settingPrefix: String = "eShare"
        private const val KEY_ASK_BEFORE_CAST = "askBeforeCast" // 是否在投屏前询问

        private const val KEY_ENABLE_NAME_ALERT_WIN = "enableAlertWindow" // 是否允许悬浮窗
        const val DEF_VALUE_ENABLE_NAME_ALERT_WIN = false

        private const val DEF_VALUE_ASK_BEFORE_CAST = false // 默认关闭投屏询问

        private const val KEY_USER_SET_HDMI_MIX = "userSetHdmiMix" // 用户是否设置过HDMI混投

        /**
         * 是否设置过HDMI混投
         */
        suspend fun isUserSetHdmiMix(): Boolean = withContext(Dispatchers.IO) {
            getValue(KEY_USER_SET_HDMI_MIX, false)
        }

        /**
         * 设置用户是否设置过HDMI混投
         */
        suspend fun setUserSetHdmiMix(userSet: Boolean) = withContext(Dispatchers.IO) {
            setValue(KEY_USER_SET_HDMI_MIX, userSet)
        }

        suspend fun isNameAlertWindowEnable(): Boolean = withContext(Dispatchers.IO) {
            getValue(KEY_ENABLE_NAME_ALERT_WIN, DEF_VALUE_ENABLE_NAME_ALERT_WIN)
        }

        suspend fun setEnableNameAlertWin(enable: Boolean = DEF_VALUE_ENABLE_NAME_ALERT_WIN) =
            withContext(Dispatchers.IO) {
                setValue(KEY_ENABLE_NAME_ALERT_WIN, enable)
            }

        fun isAskBeforeCast(): Boolean {
            return getValue(KEY_ASK_BEFORE_CAST, DEF_VALUE_ASK_BEFORE_CAST)  // 默认开启
        }

        fun setAskBeforeCast(isAsk: Boolean) {
            setValue(KEY_ASK_BEFORE_CAST, isAsk)
        }
    }

    object CloudConfigSetting : CZURSetting() {
        override val settingPrefix: String = "CloudConfig"
        private const val KEY_ENABLE_ALGO_COLLECTION = "enableAlgoCollect"

        private val systemManagerProxy by lazy { SystemManagerProxy() }


        fun isEnableAlgoCollection(): Boolean {
            return getValue(KEY_ENABLE_ALGO_COLLECTION, false)
        }

        suspend fun setEnableAlgoCollection(enable: Boolean) = withContext(Dispatchers.IO) {
            setValue(KEY_ENABLE_ALGO_COLLECTION, enable)
            syncAlgoCollectionSetting(enable)
        }

        /**
         * 同步算法开关的设置到系统
         */
        private fun syncAlgoCollectionSetting(enable: Boolean = isEnableAlgoCollection()) {
            systemManagerProxy.enableAlgoCollection(enable)
        }
    }


    /**
     * 负责维护系统设置
     */
    object SystemSetting : CZURSetting() {
        override val settingPrefix: String = "SystemSetting"
        private const val KEY_REMOVE_APK_INSTALL_RESTRICTION =
            "blockAPKInstallRemoved"  // 是否移除了APK安装的限制

        private const val KEY_EYE_PROTECTION = "eyeProtection" // 护眼模式

        fun isEyeProtectionEnable(): Boolean {
            return getValue(KEY_EYE_PROTECTION, false)
        }

        suspend fun setEyeProtectionEnable(enable: Boolean) = withContext(Dispatchers.IO) {
            setValue(KEY_EYE_PROTECTION, enable)
        }

        /**
         * 是否移除了APK安装的限制
         */
        fun isApkInstallRestrictionRemoved(): Boolean {
            return getValue(KEY_REMOVE_APK_INSTALL_RESTRICTION, false)
        }

        /**
         * 设置 是否移除了APK安装的限制
         */
        suspend fun setRemoveApkInstallRestriction(removeRestriction: Boolean) =
            withContext(Dispatchers.IO) {
                setValue(KEY_REMOVE_APK_INSTALL_RESTRICTION, removeRestriction)
            }
    }


    /**
     * 手写板的设置
     */
    object WritePadSetting : CZURSetting() {
        override val settingPrefix: String = "WritePadConfig"
        private const val KEY_ENABLE = "enable"
        private const val DEF_VALUE_WRITE_PAD_ENABLE = true

        suspend fun isEnable(): Boolean = withContext(Dispatchers.IO) {
            if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                logTagI(settingPrefix, "军工版本, 手写板功能默认关闭")
                return@withContext false
            }
            getValue(KEY_ENABLE, DEF_VALUE_WRITE_PAD_ENABLE)
        }

        suspend fun setEnable(enable: Boolean = DEF_VALUE_WRITE_PAD_ENABLE) =
            withContext(Dispatchers.IO) {
                setValue(KEY_ENABLE, enable)
            }

    }

    /**
     * 用于个性化的设置
     */
    object PersonalizationSetting : CZURSetting() {
        private const val TAG = "PersonalizationSetting"

        override val settingPrefix: String = "Launcher"

        const val KEY_LAUNCHER_LAYOUT = "launcherLayout" // Launcher的布局
        const val VALUE_LAUNCHER_LAYOUT_MEETING = 0 // 会议布局
        const val VALUE_LAUNCHER_LAYOUT_TIME = 1    // 时间日历布局
        const val VALUE_LAUNCHER_LAYOUT_STANDARD = 2 // 标准布局

        const val VALUE_LAUNCHER_LAYOUT_DEFAULT = VALUE_LAUNCHER_LAYOUT_STANDARD // 默认布局

        private const val KEY_LAUNCHER_FAV_APPS = "launcherFavApps" // Launcher的常用应用
        private const val MAX_FAV_APPS = 4 // 最多4个常用应用

        private val isOversea: Boolean =
            Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas


        private const val KEY_LAUNCHER_BOOT_APP_ENABLE = "launcherBootAppEnable"  // 开机启动应用是否开启
        const val DEF_VALUE_LAUNCHER_BOOT_APP_ENABLE = false    // 默认关闭
        private const val KEY_LAUNCHER_BOOT_APP_PKG = "launcherBootAppPkg"  // 开机启动的应用包名

        private const val KEY_LAUNCHER_SCREEN_BACKDROP = "launcherScreenBackdrop"  // Launcher的背景图

        // 自定义快捷键
        private const val KEY_SHORTCUT_SHORT_TAP = "shortTap"  // 点按
        private const val KEY_SHORTCUT_LONG_TAP = "longTap"  // 长按
        const val KEY_SHORTCUT_TYPE_SHORT_PRESS = 0
        const val KEY_SHORTCUT_TYPE_LONG_PRESS = 1


        // 默认的常用应用
        private val DEFAULT_FAV_APPS: List<String> by lazy {
            if (isOversea || Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
                listOf(
                    "com.czur.starry.device.appstore",              // 应用商店
                    "com.czur.starry.device.localmeetingrecord",    // 本地会议记录
                )
            } else {
                listOf(
                    "com.czur.starry.device.appstore",              // 应用商店
                    "com.czur.starry.device.localmeetingrecord",    // 本地会议记录
                    "com.czur.starry.device.wallpaperdisplay",      // 欢庆屏保
                )
            }
        }

        /**
         * 是否打开了开机启动应用
         */
        suspend fun isLauncherBootAppEnable(): Boolean = withContext(Dispatchers.IO) {
            getValue(KEY_LAUNCHER_BOOT_APP_ENABLE, DEF_VALUE_LAUNCHER_BOOT_APP_ENABLE)
        }

        /**
         * 设置是否打开了开机启动应用
         */
        suspend fun setLauncherBootAppEnable(enable: Boolean) = withContext(Dispatchers.IO) {
            setValue(KEY_LAUNCHER_BOOT_APP_ENABLE, enable)
            if (!enable) {
                setLauncherBootAppPkg("")  // 关闭开机启动应用时,清空包名
            }
        }

        /**
         * 获取开机启动的应用包名
         */
        suspend fun getLauncherBootAppPkg(): String = withContext(Dispatchers.IO) {
            if (!isLauncherBootAppEnable()) return@withContext ""   // 没有开启开机启动
            getValue(KEY_LAUNCHER_BOOT_APP_PKG, "")
        }

        /**
         * 设置开机启动的应用包名
         */
        suspend fun setLauncherBootAppPkg(pkg: String) = withContext(Dispatchers.IO) {
            setValue(KEY_LAUNCHER_BOOT_APP_PKG, pkg)
        }


        /**
         * 获取Launcher的常用应用
         */
        suspend fun getLauncherFavApps(): List<String> = withContext(Dispatchers.IO) {
            val favApps = getValue(KEY_LAUNCHER_FAV_APPS, "")
            favApps.split(",").filterNot { it.isEmpty() }
        }

        /**
         * 设置Launcher的常用应用
         */
        suspend fun setLauncherFavApps(favApps: List<String> = DEFAULT_FAV_APPS) =
            withContext(Dispatchers.IO) {
                val favAppsStr =
                    favApps.filterNot { it.isEmpty() }.take(MAX_FAV_APPS).joinToString(",")
                setValue(KEY_LAUNCHER_FAV_APPS, favAppsStr)
            }

        /**
         * 获取Launcher的布局设置
         */
        suspend fun getLauncherLayout(): Int = withContext(Dispatchers.IO) {
            getValue(KEY_LAUNCHER_LAYOUT, VALUE_LAUNCHER_LAYOUT_DEFAULT)
        }

        /**
         * 设置Launcher的布局模式
         */
        suspend fun setLauncherLayout(layout: Int) = withContext(Dispatchers.IO) {
            if (layout != VALUE_LAUNCHER_LAYOUT_MEETING && layout != VALUE_LAUNCHER_LAYOUT_TIME && layout != VALUE_LAUNCHER_LAYOUT_STANDARD) {
                throw IllegalArgumentException("layout must be $VALUE_LAUNCHER_LAYOUT_MEETING or $VALUE_LAUNCHER_LAYOUT_TIME")
            }
            setValue(KEY_LAUNCHER_LAYOUT, layout)
        }

        /**
         * 重置所有关于Launcher的设定到默认值
         */
        suspend fun resetLauncherConfig() {
            logTagD(TAG, "resetLauncherConfig 到默认值")
            logTagV(TAG, "setLauncherLayout: $VALUE_LAUNCHER_LAYOUT_DEFAULT")
            setLauncherLayout(VALUE_LAUNCHER_LAYOUT_DEFAULT)
            logTagV(TAG, "setLauncherFavApps: ${DEFAULT_FAV_APPS.joinToString()}")
            setLauncherFavApps(DEFAULT_FAV_APPS)
        }


        // launcher 左上角编辑名字时用的
        fun showRenameActivityDialog(context: Context, name: String) {
            val intent = Intent().apply {
                setClassName(
                    "com.czur.starry.device.settings",
                    "com.czur.starry.device.settings.ReNameDialogActivity"
                )
                putExtra("meetingRoomName", name)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_MULTIPLE_TASK) // 添加这行
            }
            context.startActivity(intent)
        }

        fun getLauncherMeetingRoomName(): String {
            return getStringSystemProp(KEY_DEVICE_LAUNCHER_MEETING_ROOM_NAME, "")
        }

        fun setLauncherMeetingRoomName(text: String) {
            setStringSystemProp(KEY_DEVICE_LAUNCHER_MEETING_ROOM_NAME, text)
        }

        /**
         * 设置自定义快捷键
         * @param customItemStr 自定义快捷键的字符串
         *  格式:"eventType;packageName"
         * @param eventType 0: 点按 1: 长按
         */
        suspend fun setCustomShortcutKey(customItemStr: String, eventType: Int) =
            withContext(Dispatchers.IO) {
                val key =
                    if (eventType == KEY_SHORTCUT_TYPE_SHORT_PRESS) KEY_SHORTCUT_SHORT_TAP else KEY_SHORTCUT_LONG_TAP
                setValue(key, customItemStr)
            }

        /**
         * 获取自定义快捷键
         * @param eventType 0: 点按 1: 长按
         * @return 自定义快捷键的字符串 格式:"eventType;packageName"
         */
        suspend fun getCustomShortcutKey(eventType: Int): String = withContext(Dispatchers.IO) {
            val key =
                if (eventType == KEY_SHORTCUT_TYPE_SHORT_PRESS) KEY_SHORTCUT_SHORT_TAP else KEY_SHORTCUT_LONG_TAP
            getValue(key, "")
        }

    }

    /**
     * Camera和Mic的设置项
     */
    object CameraAndMicSetting : CZURSetting() {
        private const val TAG = "CameraAndMicSetting"
        override val settingPrefix: String = "CameraAndMic"

        /* 算法给的参数
        audio algo parameters
        interface
            (key, int value)
        "_audio_algo_ns_level_int_": 0,
        0: no ns
        1: low level ns
        2: high level ns

        "_audio_algo_agc_level_int_": 0,
        0: normal level
        1: high level
         */
        private const val KEY_AUDIO_ALGO_NS_LEVEL = "audio_algo_ns_level" // 降噪等级
        private const val KEY_AUDIO_ALGO_AGC_LEVEL = "audio_algo_agc_level" // 拾音放大等级

        /**
         * * "_audio_algo_with_aec_int_"
         * 0: close
         * 1: open  (default)
         */
        private const val KEY_AUDIO_ALGO_AEC_LEVEL = "_audio_algo_with_aec_int_"  // 回声消除

        private const val KEY_DEFAULT_MIC = "defaultMic"            // 默认麦克风
        private const val KEY_DEFAULT_SPEAKER = "defaultSpeaker"    // 默认扬声器

        // 默认参数
        private val defNSLevel = AudioAlgoNSLevel.MID
        private val defAGCLevel = AudioAlgoAGCLevel.HIGH
        private val defAECLevel = AudioAlgoAECLevel.OPEN

        private val audioManager by lazy {
            CZURAtyManager.appContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        }

        /**
         * 降噪等级
         */
        enum class AudioAlgoNSLevel(val value: Int) {
            NONE(0),    // 关闭
            LOW(1),     // 低
            MID(2),     // 中
            HIGH(3),     // 高
        }

        /**
         * 拾音放大等级
         */
        enum class AudioAlgoAGCLevel(val value: Int) {
            NORMAL(0),   // 关闭
            HIGH(1),     // 高
        }

        enum class AudioAlgoAECLevel(val value: Int) {
            CLOSE(0),   // 关闭
            OPEN(1),     // 开启
        }

        suspend fun setAudioAlgoAECLevel(level: AudioAlgoAECLevel = defAECLevel) =
            withContext(Dispatchers.IO) {
                logTagD(TAG, "setAudioAlgoAECLevel: ${level.value}")
                audioManager.setParameters("${KEY_AUDIO_ALGO_AEC_LEVEL}=${level.value}")
            }

        suspend fun getAudioAlgoAECLevel(): AudioAlgoAECLevel = withContext(Dispatchers.IO) {
            val level = audioManager.getParameters(KEY_AUDIO_ALGO_AEC_LEVEL).prepareAudioParams()
                ?.toIntOrNull()
                ?: defAECLevel.value
            logTagD(TAG, "getAudioAlgoAECLevel: $level")
            AudioAlgoAECLevel.entries.find { it.value == level } ?: defAECLevel
        }

        suspend fun setAudioAlgoAGCLevel(level: AudioAlgoAGCLevel = defAGCLevel) =
            withContext(Dispatchers.IO) {
                logTagD(TAG, "setAudioAlgoAGCLevel: ${level.value}")
                audioManager.setParameters("${KEY_AUDIO_ALGO_AGC_LEVEL}=${level.value}")
            }

        suspend fun getAudioAlgoAGCLevel(): AudioAlgoAGCLevel = withContext(Dispatchers.IO) {
            val level = audioManager.getParameters(KEY_AUDIO_ALGO_AGC_LEVEL).prepareAudioParams()
                ?.toIntOrNull()
                ?: defAGCLevel.value
            logTagD(TAG, "getAudioAlgoAGCLevel: $level")
            AudioAlgoAGCLevel.entries.find { it.value == level } ?: defAGCLevel
        }

        suspend fun setAudioAlgoNSLevel(level: AudioAlgoNSLevel = defNSLevel) =
            withContext(Dispatchers.IO) {
                logTagD(TAG, "setAudioAlgoNSLevel: ${level.value}")
                audioManager.setParameters("${KEY_AUDIO_ALGO_NS_LEVEL}=${level.value}")
            }

        suspend fun getAudioAlgoNSLevel(): AudioAlgoNSLevel = withContext(Dispatchers.IO) {
            val level = audioManager.getParameters(KEY_AUDIO_ALGO_NS_LEVEL).prepareAudioParams()
                ?.toIntOrNull()
                ?: defNSLevel.value
            logTagD(TAG, "getAudioAlgoNSLevel: $level")
            AudioAlgoNSLevel.entries.find { it.value == level } ?: defNSLevel
        }

        /**
         * 获取默认麦克风类型
         * 从系统属性 persist.czur.intype 读取
         */
        suspend fun getDefaultMicType(): Int {
            val deviceType = withContext(Dispatchers.IO) {
                getStringSystemProp("persist.czur.intype", "-1").toIntOrNull() ?: -1
            }
            logTagD(TAG, "getDefaultMicType: $deviceType")
            return deviceType
        }

        /**
         * 获取默认扬声器类型
         * 从系统属性 persist.czur.outtype 读取
         */
        suspend fun getDefaultSpeakerType(): Int {
            val deviceType = withContext(Dispatchers.IO) {
                getStringSystemProp("persist.czur.outtype", "-1").toIntOrNull() ?: -1
            }
            logTagD(TAG, "getDefaultSpeakerType: $deviceType")
            return deviceType
        }

        /**
         * 获取默认麦克风 (兼容旧版本)
         * @deprecated 使用 getDefaultMicType() 替代
         */
        @Deprecated("使用 getDefaultMicType() 替代")
        suspend fun getDefaultMic(): Int {
            val deviceID = withContext(Dispatchers.IO) {
                getValue(KEY_DEFAULT_MIC, -1)
            }
            logTagD(TAG, "getDefaultMic (deprecated): $deviceID")
            return deviceID
        }

        /**
         * 获取默认扬声器 (兼容旧版本)
         * @deprecated 使用 getDefaultSpeakerType() 替代
         */
        @Deprecated("使用 getDefaultSpeakerType() 替代")
        suspend fun getDefaultSpeaker(): Int {
            val deviceID = withContext(Dispatchers.IO) {
                getValue(KEY_DEFAULT_SPEAKER, -1)
            }
            logTagD(TAG, "getDefaultSpeaker (deprecated): $deviceID")
            return deviceID
        }

        /**
         * 从参数中提取音频算法的参数
         * Format:
         *  audio_algo_ns_level=3
         */
        private fun String?.prepareAudioParams(): String? {
            logTagV(TAG, "prepareAudioParams: $this")
            return this?.let {
                val params = it.split("=")
                params.getOrNull(1)?.trim()
            }
        }
    }

    abstract class CZURSetting {
        abstract val settingPrefix: String  // 所有Key的前缀
        private val callbackList by lazy {
            initObserver()
            mutableListOf<(key: String) -> Unit>()
        }

        // 根据LifecycleOwner来管理监听
        private var lifeCycleMap = mutableMapOf<LifecycleOwner, List<(key: String) -> Unit>>()

        private fun initObserver() {
            val uri = if (settingPrefix.isEmpty()) {
                Settings.Global.CONTENT_URI
            } else {
                Settings.Global.CONTENT_URI.buildUpon().appendPath(settingPrefix)
                    .build()
            }

            val observer = object : ContentObserver(observerHandler) {
                override fun onChange(selfChange: Boolean, uri: Uri?) {
                    super.onChange(selfChange, uri)
                    if (uri == null) return
                    val key = uri.lastPathSegment ?: return
                    callbackList.forEach { it(key) }
                }
            }

            cr.registerContentObserver(uri, true, observer)
        }

        fun getRealKey(key: String): String {
            return if (settingPrefix.isEmpty()) key else "${settingPrefix}/$key"
        }


        /**
         * 注册监听
         */
        fun registerObserver(callback: (key: String) -> Unit) {
            callbackList.add(callback)
        }

        fun unRegisterObserver(callback: (key: String) -> Unit) {
            callbackList.remove(callback)
        }

        /**
         * 根据LifecycleOwner来管理监听
         */
        fun registerObserver(lifecycleOwner: LifecycleOwner, callback: (key: String) -> Unit) {
            if (!lifeCycleMap.containsKey(lifecycleOwner)) {
                lifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
                    override fun onDestroy(owner: LifecycleOwner) {
                        super.onDestroy(owner)
                        val list =
                            lifeCycleMap[lifecycleOwner]?.toMutableList() ?: mutableListOf()
                        list.forEach {
                            unRegisterObserver(it)
                        }
                        lifeCycleMap.remove(lifecycleOwner)
                    }
                })
            }
            val list = lifeCycleMap[lifecycleOwner]?.toMutableList() ?: mutableListOf()
            list.add(callback)
            lifeCycleMap[lifecycleOwner] = list
            registerObserver(callback)
        }

        fun <T> setValue(key: String, value: T) {
            val realKey = getRealKey(key)
            when (value) {
                is String -> Settings.Global.putString(cr, realKey, value)
                is Int -> Settings.Global.putInt(cr, realKey, value)
                is Float -> Settings.Global.putFloat(cr, realKey, value)
                is Long -> Settings.Global.putLong(cr, realKey, value)
                is Boolean -> Settings.Global.putInt(cr, realKey, value.toInt())
                else -> NotImpl()
            }
        }

        inline fun <reified T> getValue(key: String, defValue: T = defValue()): T {
            val realKey = getRealKey(key)
            val res = when (defValue) {
                is String -> Settings.Global.getString(cr, realKey) ?: defValue
                is Int -> Settings.Global.getInt(cr, realKey, defValue)
                is Float -> Settings.Global.getFloat(cr, realKey, defValue)
                is Long -> Settings.Global.getLong(cr, realKey, defValue)
                is Boolean -> Settings.Global.getInt(cr, realKey, defValue.toInt()).toBoolean()
                else -> NotImpl()
            }
            return res as T
        }

        inline fun <reified T> defValue(): T {
            return when (T::class.java) {
                String::class.java -> ""
                Int::class.java, java.lang.Integer::class.java -> 0
                Long::class.java, java.lang.Long::class.java -> 0L
                Float::class.java, java.lang.Float::class.java -> 0F
                Double::class.java, java.lang.Double::class.java -> .0
                Boolean::class.java, java.lang.Boolean::class.java -> false
                else -> NotImpl()
            } as T
        }

        fun Boolean.toInt(): Int = if (this) VALUE_BOOL_TRUE else VALUE_BOOL_FALSE
        fun Int.toBoolean(): Boolean = this == VALUE_BOOL_TRUE
    }

}

