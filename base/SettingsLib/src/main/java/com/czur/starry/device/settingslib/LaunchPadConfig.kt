package com.czur.starry.device.settingslib

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.ServerEnv
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.hw.StarryModel

/**
 * Created by 陈丰尧 on 2023/2/3
 * 配置Launcher显示的应用黑名单等信息
 *
 */
// 不显示的黑名单
val blackPkgList by lazy {
    val blackList = when (Constants.serverEnv) {
        ServerEnv.PRODUCT -> commonBlackPkgList + productBlackPkgList
        ServerEnv.DEVELOP -> commonBlackPkgList
    }.toMutableList()

    if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
        blackList.add("com.czur.starry.device.wallpaperdisplay")    // StudioSPlus不显示欢庆屏保
    }

    when (Constants.starryHWInfo.salesLocale) {
        StarryDevLocale.Mainland -> blackList
        StarryDevLocale.Overseas -> blackList + overseasBlackPkgList
    }.toSet()
}

// 通用黑名单
private val commonBlackPkgList = listOf(
    "com.android.settings",
    "com.android.calendar",
    "com.android.camera2",
    "com.android.contacts",
    "com.android.deskclock",
    "com.android.email",
    "com.android.soundrecorder",
    "com.android.calculator2",
    "com.android.documentsui",
    "com.android.quicksearchbox",
    "com.czur.keystone",
    "com.ecloud.eairplay",
    "com.ecloud.eshare.server",
    "com.czur.starry.device.starrypad",
    "com.sohu.inputmethod.sogou",       // 搜狗拼音
    "com.android.gallery3d",            // 图库
    "com.DeviceTest",                   // 厂测
    "com.czur.starry.devicetest",       // 厂测android14
    "com.hysd.hyscreen",                // 华研对焦的app
    "com.hysd.client",
)

// 生产环境特殊的黑名单
private val productBlackPkgList = listOf<String>()

// 海外版黑名单
private val overseasBlackPkgList = listOf(
    "com.czur.starry.device.wallpaperdisplay",   // 海外版不显示欢庆屏保
)


// 系统应用名单
val systemPkgList by lazy {
    val commonSystemPkgList = when (Constants.serverEnv) {
        ServerEnv.PRODUCT -> commonSystemPkgList
        ServerEnv.DEVELOP -> commonSystemPkgList
    }
    when (Constants.starryHWInfo.salesLocale) {
        StarryDevLocale.Mainland -> commonSystemPkgList
        StarryDevLocale.Overseas -> commonSystemPkgList
    }
}

// 通用系统名单
private val commonSystemPkgList = listOf(
    "com.czur.starry.device.starrypad",
    "com.czur.starry.device.localmeetingrecord",
    "com.czur.starry.device.appstore",
    "com.czur.starry.device.meeting",
    "com.czur.starry.device.file",
    "com.czur.starry.device.personalcenter",
    "com.czur.starry.device.wallpaperdisplay",
    "com.czur.starry.device.settings",
    "com.czur.starry.device.hdmiin"
)